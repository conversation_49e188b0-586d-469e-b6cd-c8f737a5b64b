"use client";

import Image from "next/image";
import { useViewerCount } from "@/hooks/useViewerCount";

interface MobileActionButtonsProps {
  blvInfo: {
    displayName?: string;
  };
}

export function MobileActionButtons({ blvInfo }: MobileActionButtonsProps) {
  const { formattedViewerCount } = useViewerCount({
    minVariation: -150,
    maxVariation: 250,
    minInterval: 3000,
    maxInterval: 7000,
  });

  return (
    <div className="flex gap-2 overflow-x-auto px-1 pb-1 lg:hidden">
      {/* Live Stream Button */}
      <button className="inline-flex items-center justify-center whitespace-nowrap cursor-pointer rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow-sm hover:bg-primary/90 h-8 gap-0 bg-gradient-to-r from-[#1E40AF] to-[#3B82F6] px-3 py-[6px]">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-radio size-5">
          <path d="M4.9 19.1C1 15.2 1 8.8 4.9 4.9"></path>
          <path d="M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5"></path>
          <circle cx="12" cy="12" r="2"></circle>
          <path d="M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5"></path>
          <path d="M19.1 4.9C23 8.8 23 15.1 19.1 19"></path>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-dot size-5">
          <circle cx="12.1" cy="12.1" r="1"></circle>
        </svg>
        {/* Viewers Count */}
        <div className="inline-flex items-center px-2.5 py-0.5 justify-center rounded-full bg-white text-xs font-normal text-black">{formattedViewerCount}</div>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-dot size-5">
          <circle cx="12.1" cy="12.1" r="1"></circle>
        </svg>
        <span className="text-xs font-normal">
          {blvInfo?.displayName}
        </span>
      </button>

      {/* Sponsor Logos */}
      <div className="flex gap-1 border rounded-lg">
        <a target="_blank" className="inline-flex h-8 items-center justify-center min-w-20" href="https://ngoaihangtv.me/">
          <Image alt="KUDV Logo" loading="lazy" width={56} height={36} decoding="async" className="object-cover h-7 w-full max-w-none" src="/vendor/ok-logo.png" />
        </a>
      </div>

      {/* Other Commentators Button */}
      {/* <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-8 px-3 py-[6px]" type="button">
        <svg xmlns="http://www.w3.org/2000/svg" className="size-6" width="25" height="24" viewBox="0 0 25 24" fill="none">
          <path d="M12.5 2C6.986 2 2.5 6.486 2.5 12V16.143C2.5 17.167 3.397 18 4.5 18H5.5C5.76522 18 6.01957 17.8946 6.20711 17.7071C6.39464 17.5196 6.5 17.2652 6.5 17V11.857C6.5 11.5918 6.39464 11.3374 6.20711 11.1499C6.01957 10.9624 5.76522 10.857 5.5 10.857H4.592C5.148 6.987 8.478 4 12.5 4C16.522 4 19.852 6.987 20.408 10.857H19.5C19.2348 10.857 18.9804 10.9624 18.7929 11.1499C18.6054 11.3374 18.5 11.5918 18.5 11.857V18C18.5 19.103 17.603 20 16.5 20H14.5V19H10.5V22H16.5C18.706 22 20.5 20.206 20.5 18C21.603 18 22.5 17.167 22.5 16.143V12C22.5 6.486 18.014 2 12.5 2Z" fill="url(#paint0_linear_310_39713)"></path>
          <defs><linearGradient id="paint0_linear_310_39713" x1="22.5" y1="12" x2="2.5" y2="12" gradientUnits="userSpaceOnUse"><stop stopColor="#3B82F6"></stop><stop offset="1" stopColor="#3B82F6"></stop></linearGradient></defs>
        </svg>
        <span className="text-muted-foreground text-base font-normal">Bình luận viên khác</span>
        <div className="border px-2.5 py-0.5 transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 flex size-5 items-center justify-center rounded-full bg-white text-xs font-normal text-black hover:bg-white">2</div>
      </button> */}

      {/* Share Button */}
      <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer rounded-md text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-8 px-3 py-[6px]" type="button">
        <svg xmlns="http://www.w3.org/2000/svg" className="size-6" width="25" height="24" viewBox="0 0 25 24" fill="none">
          <path d="M15.17 3.07515C15.2963 3.01346 15.4373 2.98849 15.5771 3.00308C15.7168 3.01766 15.8497 3.07122 15.9605 3.15765L22.7105 8.40765C22.7975 8.47523 22.8686 8.56117 22.9186 8.65933C22.9687 8.75749 22.9965 8.86547 23 8.97559C23.0036 9.08572 22.9829 9.19528 22.9393 9.29648C22.8958 9.39769 22.8304 9.48805 22.748 9.56115L15.998 15.5611C15.8899 15.6571 15.7564 15.7198 15.6135 15.7417C15.4705 15.7636 15.3244 15.7437 15.1925 15.6844C15.0606 15.6251 14.9487 15.529 14.8702 15.4076C14.7917 15.2862 14.75 15.1447 14.75 15.0001V12.0841C14.396 12.1441 13.934 12.2491 13.394 12.4291C12.0845 12.8671 10.313 13.7476 8.531 15.5311C8.42197 15.6406 8.28179 15.7137 8.12964 15.7405C7.97748 15.7673 7.82078 15.7464 7.68093 15.6807C7.54108 15.6151 7.42492 15.5078 7.34833 15.3737C7.27173 15.2395 7.23844 15.085 7.253 14.9311C7.4675 12.5746 8.1545 10.8556 9.089 9.61665C9.89149 8.54765 10.9814 7.72882 12.2315 7.25565C13.0382 6.95067 13.8884 6.77648 14.75 6.73965V3.75015C14.7497 3.60958 14.7889 3.47177 14.8632 3.35242C14.9375 3.23307 15.0438 3.137 15.17 3.07515ZM3.5 8.25015C3.5 7.25559 3.89509 6.30176 4.59835 5.5985C5.30161 4.89524 6.25544 4.50015 7.25 4.50015H10.25C10.4489 4.50015 10.6397 4.57916 10.7803 4.71982C10.921 4.86047 11 5.05123 11 5.25015C11 5.44906 10.921 5.63982 10.7803 5.78048C10.6397 5.92113 10.4489 6.00015 10.25 6.00015H7.25C6.65326 6.00015 6.08097 6.2372 5.65901 6.65916C5.23705 7.08111 5 7.65341 5 8.25015V17.2501C5 17.8469 5.23705 18.4192 5.65901 18.8411C6.08097 19.2631 6.65326 19.5001 7.25 19.5001H16.25C16.8467 19.5001 17.419 19.2631 17.841 18.8411C18.2629 18.4192 18.5 17.8469 18.5 17.2501V15.7501C18.5 15.5512 18.579 15.3605 18.7197 15.2198C18.8603 15.0792 19.0511 15.0001 19.25 15.0001C19.4489 15.0001 19.6397 15.0792 19.7803 15.2198C19.921 15.3605 20 15.5512 20 15.7501V17.2501C20 18.2447 19.6049 19.1985 18.9017 19.9018C18.1984 20.6051 17.2446 21.0001 16.25 21.0001H7.25C6.25544 21.0001 5.30161 20.6051 4.59835 19.9018C3.89509 19.1985 3.5 18.2447 3.5 17.2501V8.25015Z" fill="url(#paint0_linear_310_39718)"></path>
          <defs><linearGradient id="paint0_linear_310_39718" x1="23.0004" y1="11.9996" x2="3.5" y2="11.9996" gradientUnits="userSpaceOnUse"><stop stopColor="#3B82F6"></stop><stop offset="1" stopColor="#1E40AF"></stop></linearGradient></defs>
        </svg>
        <span className="text-muted-foreground text-base font-normal">Chia sẻ phiên trực tiếp</span>
      </button>
    </div>
  );
}

interface MobileScreenLockButtonProps {
  isScreenLocked: boolean;
  toggleScreenLock: () => void;
}

export function MobileScreenLockButton({ isScreenLocked, toggleScreenLock }: MobileScreenLockButtonProps) {
  return (
    <button
      onClick={toggleScreenLock}
      className={`lg:hidden flex-shrink-0 px-2 sm:px-3 py-1 sm:py-2 text-center text-xs font-medium transition-all duration-200 whitespace-nowrap rounded-full flex items-center gap-2 ${isScreenLocked
        ? "text-green-600 border-2 border-green-600 bg-green-50 dark:bg-green-900/20 shadow-sm"
        : "text-gray-500 dark:text-gray-400 border-0 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-white dark:hover:bg-gray-700"
        }`}
    >
      {isScreenLocked && (
        <span className="font-medium text-xs">Đã khóa</span>
      )}
      <div className="w-8 h-6 sm:w-10 sm:h-7 flex items-center justify-center">
        <Image
          src={isScreenLocked ? "/icon/un-lock.svg" : "/icon/lock.svg"}
          alt={isScreenLocked ? "Unlock" : "Lock"}
          width={20}
          height={20}
          className={`w-6 h-6 sm:w-8 sm:h-8 transition-all duration-200 ${isScreenLocked
            ? "filter brightness-0 invert-[0.4] sepia-[1] saturate-[5] hue-rotate-[120deg]"
            : "filter brightness-0 invert-[0.6] dark:invert-[0.4]"
            }`}
        />
      </div>
    </button>
  );
}
