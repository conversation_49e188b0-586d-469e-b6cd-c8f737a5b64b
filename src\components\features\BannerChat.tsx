"use client";

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useChatService } from '@/hooks/useChatService';
import { useSupabase } from '@/contexts/SupabaseContext';
import { useRealtime } from '@/hooks/useRealtime';

export interface Expert {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar_url: string | null;
}

export interface BannerData {
  id: string;
  expert: Expert;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  sent_at: string | null;
  type: string;
  order?: string;
  displayed?: string;
  winDisplayed?: string;
  updatedAt?: string;
}

interface ChatMessage {
  id: string;
  content: string;
  created_at: string;
  updated_at?: string;
  sender_id?: string;
}

class LocalBannerService {
  private readonly STORAGE_KEY = 'banner_read_status';

  getBannerReadStatus(bannerId: string): boolean {
    if (typeof window === 'undefined') return false;
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return false;
      const readStatus = JSON.parse(stored);
      return readStatus[bannerId] === true;
    } catch (error) {
      console.error('Error getting banner read status:', error);
      return false;
    }
  }

  markBannerAsRead(bannerId: string): boolean {
    if (typeof window === 'undefined') return false;
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const readStatus = stored ? JSON.parse(stored) : {};
      readStatus[bannerId] = true;
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(readStatus));
      return true;
    } catch (error) {
      console.error('Error marking banner as read:', error);
      return false;
    }
  }

  getReadBannerIds(): Set<string> {
    if (typeof window === 'undefined') return new Set();
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return new Set();
      const readStatus = JSON.parse(stored);
      const readIds = Object.keys(readStatus).filter(id => readStatus[id] === true);
      return new Set(readIds);
    } catch (error) {
      console.error('Error getting read banner IDs:', error);
      return new Set();
    }
  }

  clearAllReadStatus(): boolean {
    if (typeof window === 'undefined') return false;
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing banner read status:', error);
      return false;
    }
  }

  getAllReadStatus(): Record<string, boolean> {
    if (typeof window === 'undefined') return {};
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error getting all read status:', error);
      return {};
    }
  }
}

interface BannerChatProps {
  onOpenAuthModal?: (mode: 'login' | 'register') => void;
  onTabChange?: (tabIndex: number, roomId?: string) => void;
}

export default function BannerChat({
  onOpenAuthModal,
  onTabChange
}: BannerChatProps) {
  const [bannerData, setBannerData] = useState<BannerData[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [showBanner, setShowBanner] = useState(false);
  const [readBannerIds, setReadBannerIds] = useState<Set<string>>(new Set());
  const [userJoinTime, setUserJoinTime] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [bannerQueue, setBannerQueue] = useState<BannerData[]>([]);
  const [isDisplayingBanner, setIsDisplayingBanner] = useState(false);

  const router = useRouter();
  const { user, reloadChatRooms } = useAuth();
  const { createChat } = useChatService(user?.id || '');
  const { supabase } = useSupabase();

  // Use useRef to avoid recreating the service on every render
  const localBannerServiceRef = useRef(new LocalBannerService());
  const bannerTimerRef = useRef<NodeJS.Timeout | null>(null);

  const BANNER_ROOM_ID = '22dbb62a-8ff0-4095-bc8a-574106709db3';

  // Fetch banner data - now with proper dependencies
  const fetchBannerData = useCallback(async () => {
    if (!supabase || !userJoinTime) return;

    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          id,
          content,
          message_type,
          created_at,
          updated_at,
          sender_id
        `)
        .eq('room_id', BANNER_ROOM_ID)
        .eq('message_type', 'text')
        .gt('created_at', userJoinTime)
        .order('created_at', { ascending: true })
        .order('id', { ascending: true });

      if (error) return;

      if (data && data.length > 0) {
        const bannerMessages = data.filter(item => {
          try {
            const content = JSON.parse(item.content);
            return content.type && (content.type.includes('KÈO') || content.type.includes('VIP'));
          } catch {
            return false;
          }
        });

        const formattedData: BannerData[] = bannerMessages.map((item: ChatMessage) => {
          const content = JSON.parse(item.content);
          return {
            id: content.id || item.id,
            expert: content.expert || {
              id: 'default',
              name: 'AD Long Thiên',
              role: 'admin',
              email: '<EMAIL>',
              avatar_url: null
            },
            tournament: content.tournament || 'BÓNG ĐÁ - GIẢI LIGUE 1 PHÁP [FT]',
            team_name: content.team_name || 'LORIENT (H) vs STADE RENNAIS',
            closing_bet: content.closing_bet || '2.15 TÀI',
            saying: content.saying || 'Tin nhắn',
            status: content.status,
            created_at: content.created_at || item.created_at,
            updated_at: content.updated_at || item.updated_at,
            created_by: content.created_by || item.sender_id,
            sent_at: content.sent_at,
            type: content.type || 'KÈO VIP'
          };
        });

        setBannerData(formattedData);
      }
    } catch (error) {
      console.error('Error fetching banner data:', error);
    }
  }, [supabase, userJoinTime]);

  // Realtime subscription - moved outside and simplified
  useRealtime({
    table: 'messages',
    filter: `room_id=eq.${BANNER_ROOM_ID}`,
    onInsert: useCallback((payload: { new: Record<string, unknown> }) => {
      console.log('New message received via realtime:', payload.new);
      // Trigger refetch when new message arrives
      if (userJoinTime) {
        fetchBannerData();
      }
    }, [fetchBannerData, userJoinTime])
  });

  // Mark banner as read - stable function
  const markBannerAsRead = useCallback((bannerId: string) => {
    const success = localBannerServiceRef.current.markBannerAsRead(bannerId);
    if (success) {
      setReadBannerIds(prev => new Set([...prev, bannerId]));
    }
  }, []);

  // Display next banner - stable function
  const displayNextBanner = useCallback((queue: BannerData[]) => {
    if (queue.length === 0) {
      setShowBanner(false);
      setIsDisplayingBanner(false);
      return;
    }

    const nextBanner = queue[0];
    const bannerIndex = bannerData.findIndex(b => b.id === nextBanner.id);
    
    if (bannerIndex >= 0) {
      setCurrentBannerIndex(bannerIndex);
      setShowBanner(true);
      setIsDisplayingBanner(true);
      
      const displayDuration = nextBanner.status?.trim() === 'win' ? 15000 : 10000;
      
      if (bannerTimerRef.current) {
        clearTimeout(bannerTimerRef.current);
      }
      
      bannerTimerRef.current = setTimeout(() => {
        markBannerAsRead(nextBanner.id);
        const remainingQueue = queue.slice(1);
        setBannerQueue(remainingQueue);
        
        setTimeout(() => {
          displayNextBanner(remainingQueue);
        }, 500);
      }, displayDuration);
    }
  }, [bannerData, markBannerAsRead]);

  // Initialize user join time - only once
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const USER_JOIN_TIME_KEY = 'user_join_time';
      let joinTime = localStorage.getItem(USER_JOIN_TIME_KEY);
      
      if (!joinTime) {
        joinTime = new Date().toISOString();
        localStorage.setItem(USER_JOIN_TIME_KEY, joinTime);
      }
      
      setUserJoinTime(joinTime);
    }
  }, []);

  // Load read banner IDs - only once
  useEffect(() => {
    const readIds = localBannerServiceRef.current.getReadBannerIds();
    setReadBannerIds(readIds);
  }, []);

  // Fetch banner data when dependencies are ready
  useEffect(() => {
    if (userJoinTime && supabase) {
      fetchBannerData();
    }
  }, [fetchBannerData]);

  // Update banner queue when banner data or read status changes
  useEffect(() => {
    if (bannerData.length > 0) {
      const unreadBanners = bannerData.filter(banner => !readBannerIds.has(banner.id));

      if (unreadBanners.length > 0) {
        setBannerQueue(unreadBanners);
        
        if (!isDisplayingBanner) {
          displayNextBanner(unreadBanners);
        }
      } else {
        setBannerQueue([]);
        setShowBanner(false);
        setIsDisplayingBanner(false);
      }
    } else {
      setBannerQueue([]);
      setShowBanner(false);
      setIsDisplayingBanner(false);
    }
  }, [bannerData, readBannerIds, isDisplayingBanner, displayNextBanner]);

  // Handle banner click
  const handleBannerClick = async () => {
    if (!user) {
      onOpenAuthModal?.('login');
      return;
    }

    const currentBanner = bannerData[currentBannerIndex];
    if (!currentBanner) return;

    if (bannerTimerRef.current) {
      clearTimeout(bannerTimerRef.current);
      bannerTimerRef.current = null;
    }
    
    markBannerAsRead(currentBanner.id);
    const remainingQueue = bannerQueue.filter(b => b.id !== currentBanner.id);
    setBannerQueue(remainingQueue);
    
    try {
      const chatName = `Chat với ${currentBanner.expert.name}`;
      const chatType = 'direct' as const;

      const { room, error } = await createChat(chatName, chatType);

      if (error) return;

      if (room) {
        // Reload chatRooms in useAuth context to include the new room
        await reloadChatRooms();
        
        // Wait a bit for the context to update
        setTimeout(() => {
          onTabChange?.(2, room.id);
        }, 100);
      }
    } catch (error) {
      console.error('Error creating chat:', error);
    }
    
    setTimeout(() => {
      displayNextBanner(remainingQueue);
    }, 500);
  };

  // Animation effect
  useEffect(() => {
    if (showBanner && bannerData.length > 0 && bannerData[currentBannerIndex]) {
      setIsVisible(false);
      setIsContentVisible(false);

      const timer1 = setTimeout(() => {
        setIsVisible(true);
      }, 100);

      const timer2 = setTimeout(() => {
        setIsContentVisible(true);
      }, 400);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
      };
    } else {
      setIsVisible(false);
      setIsContentVisible(false);
    }
  }, [showBanner, bannerData, currentBannerIndex]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (bannerTimerRef.current) {
        clearTimeout(bannerTimerRef.current);
      }
    };
  }, []);

  if (!showBanner || bannerData.length === 0 || !bannerData[currentBannerIndex]) {
    return null;
  }

  const currentBanner = bannerData[currentBannerIndex];

  return (
    <div
      className={`absolute top-0 left-0 right-0 z-10 transition-all duration-700 ease-out cursor-pointer ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-8'
        }`}
      onClick={handleBannerClick}
    >
      <div className="relative overflow-hidden">
        <img
          src="/banner-chat/5.png"
          alt="Banner Background"
          className="w-full h-20 object-cover"
        />

        <img
          src="/banner-chat/6.png"
          alt="Banner Secondary"
          className="w-full h-7 object-cover"
        />

        <div className="absolute bottom-0 left-0 right-0 h-7 flex items-center">
          <div className="flex-[0.6] h-full flex items-center px-3 gap-2">
            <img
              src="/banner-chat/3.png"
              alt="Chat Icon"
              className="w-5 h-5 object-contain ml-1"
            />
            <span className="text-white text-[13px] font-medium">
              {currentBanner.saying || 'Tin nhắn'}
            </span>
          </div>

          <div className="flex-[0.4] bg-red-800 h-7 flex items-center px-3 gap-2">
            <img
              src="/banner-chat/4.png"
              alt="Hand Icon"
              className="w-4 h-4 object-contain"
            />
            <span className="text-white text-[11px] font-bold uppercase text-center">
              {currentBanner.type || 'KÈO VIP'}
            </span>
          </div>
        </div>

        <div className="absolute top-0 left-0 right-0 h-20 bg-black/30 flex items-center px-1">
          <div className="flex-3 flex items-center pl-0" style={{ width: '40%' }}>
            <div className="relative" style={{ marginLeft: '10px' }}>
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center overflow-hidden">
                {currentBanner.expert.avatar_url ? (
                  <img
                    src={currentBanner.expert.avatar_url}
                    alt="Expert Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <img
                    src="/banner-chat/1.png"
                    alt="Default Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                )}
              </div>
              <div className="absolute -bottom-0 -right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
            </div>

            <div className="flex flex-col ml-1 min-w-0">
              <span className="text-white font-bold text-xs drop-shadow-lg truncate">
                {currentBanner.expert.name || 'AD Long Thiên'}
              </span>
              <span className="text-white/80 text-xs drop-shadow-lg truncate">Đang hoạt động</span>
            </div>
          </div>

          <div className="flex-4 flex flex-col items-center text-left px-1 relative" style={{ width: '45%' }}>
            <div className={`text-white/90 text-xs font-medium drop-shadow-lg mb-1 transition-opacity duration-300 ${currentBanner.status?.trim() === 'win' ? 'opacity-30' : 'opacity-90'
              }`}>
              {currentBanner.tournament || 'BÓNG ĐÁ - GIẢI LIGUE 1 PHÁP [FT]'}
            </div>
            <div className={`text-yellow-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${currentBanner.status?.trim() === 'win' ? 'opacity-30' : 'opacity-100'
              }`}>
              {currentBanner.team_name || 'LORIENT (H) vs STADE RENNAIS'}
            </div>
            {currentBanner.status?.trim() === 'win' && (
              <img
                src="/banner-chat/2.png"
                alt="Kèo Thắng"
                className="absolute inset-0 w-full h-full object-contain opacity-90 transform -rotate-12 origin-center"
              />
            )}
          </div>

          <div className="flex-3 flex flex-col items-center justify-center" style={{ width: '20%' }}>
            <span className={`text-green-400 font-bold text-lg drop-shadow-lg transition-opacity duration-300 ${currentBanner.status?.trim() === 'win' ? 'opacity-30' : 'opacity-100'
              }`}>
              {currentBanner.closing_bet?.split(' ')[0] || ''}
            </span>
            <span className={`text-green-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${currentBanner.status?.trim() === 'win' ? 'opacity-30' : 'opacity-100'
              }`}>
              {currentBanner.closing_bet?.split(' ').slice(1).join(' ') || ''}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}