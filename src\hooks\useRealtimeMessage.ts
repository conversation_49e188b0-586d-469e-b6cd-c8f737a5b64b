import { useSupabase } from "@/contexts/SupabaseContext";
import { ChatService } from "@/services/chat.service";
import { ChatMessage } from "@/types";
import { useCallback, useEffect, useRef, useState } from "react";

export const useRealtimeMessage = (roomId: string | null) => {
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const { supabase } = useSupabase();
  const subscriptionRef = useRef<(() => void) | null>(null);

  const getMessages = useCallback(
    async (roomId: string) => {
      setLoading(true);
      const chatService = new ChatService(supabase);
      const { messages, error } = await chatService.getMessages(roomId);
      if (error) {
        console.error("Error fetching messages:", error);
        return;
      }
      setMessages(messages);
      setLoading(false);
    },
    [supabase]
  );

  useEffect(() => {
    if (roomId) {
      getMessages(roomId);
    }
  }, [getMessages, roomId]);

  useEffect(() => {
    if (!roomId) {
      return;
    }
    if (subscriptionRef.current) {
      subscriptionRef.current();
    }

    // Create new chat service instance
    const chatService = new ChatService(supabase);

    // Subscribe to new room
    const unsubscribe = chatService.subscribeToMessages(
      roomId,
      (newMessage: ChatMessage) => {
        setMessages((prev) => [...prev, newMessage]);
      }
    );
    subscriptionRef.current = unsubscribe;

    return () => {
      unsubscribe();
    };
  }, [roomId, supabase]);

  return {
    messages,
    loading,
  };
};
