"use client";

import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";

interface ScreenLockContextType {
  isScreenLocked: boolean;
  setIsScreenLocked: (locked: boolean) => void;
}

const ScreenLockContext = createContext<ScreenLockContextType | undefined>(
  undefined
);

export function ScreenLockProvider({ children }: { children: ReactNode }) {
  const [isScreenLocked, setIsScreenLocked] = useState(false);

  useEffect(() => {
    if (isScreenLocked) {
      const scrollY = window.scrollY;

      // Khóa scroll của toàn bộ trang
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";

      document.body.classList.add("screen-locked");

      // Thêm CSS để cho phép scroll trong chat container
      const style = document.createElement("style");
      style.id = "screen-lock-chat-scroll";
      style.textContent = `
        .screen-locked [data-chat-container] {
          overflow-y: auto !important;
          -webkit-overflow-scrolling: touch !important;
          overscroll-behavior: contain !important;
        }
        .screen-locked [data-chat-container]::-webkit-scrollbar {
          width: 4px;
        }
        .screen-locked [data-chat-container]::-webkit-scrollbar-track {
          background: transparent;
        }
        .screen-locked [data-chat-container]::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.5);
          border-radius: 2px;
        }
        .screen-locked [data-chat-container]::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.8);
        }
      `;
      document.head.appendChild(style);
    } else {
      // Khôi phục scroll của trang
      const scrollY = document.body.style.top;
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";

      document.body.classList.remove("screen-locked");

      // Xóa CSS cho chat scroll
      const style = document.getElementById("screen-lock-chat-scroll");
      if (style) {
        style.remove();
      }

      // Khôi phục vị trí scroll
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || "0") * -1);
      }
    }

    return () => {
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
      document.body.classList.remove("screen-locked");

      const style = document.getElementById("screen-lock-chat-scroll");
      if (style) {
        style.remove();
      }
    };
  }, [isScreenLocked]);

  return (
    <ScreenLockContext.Provider value={{ isScreenLocked, setIsScreenLocked }}>
      {children}
    </ScreenLockContext.Provider>
  );
}

export function useScreenLock() {
  const context = useContext(ScreenLockContext);
  if (context === undefined) {
    throw new Error("useScreenLock must be used within a ScreenLockProvider");
  }
  return context;
}
