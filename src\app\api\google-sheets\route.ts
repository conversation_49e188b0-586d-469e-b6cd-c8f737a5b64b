import { NextRequest, NextResponse } from 'next/server';
import GoogleSheetsService from '@/services/googleSheetsService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const spreadsheetId = searchParams.get('spreadsheetId');
    const range = searchParams.get('range');
    const sheet = searchParams.get('sheet');
    
    if (!spreadsheetId) {
      return NextResponse.json(
        {
          success: false,
          error: 'spreadsheetId is required'
        },
        { status: 400 }
      );
    }
    
    let data;
    
    if (range) {
      // If specific range is provided
      data = await GoogleSheetsService.getSheetData(spreadsheetId, range);
    } else if (sheet) {
      // If only sheet name is provided, get entire sheet
      data = await GoogleSheetsService.getSheetDataByRange(spreadsheetId, sheet);
    } else {
      // Default: get 'lenkeo' sheet
      data = await GoogleSheetsService.getLenKeoData(spreadsheetId);
    }

    return NextResponse.json({
      success: true,
      data: data
    });

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { spreadsheetId, ranges } = body;

    if (!spreadsheetId) {
      return NextResponse.json(
        {
          success: false,
          error: 'spreadsheetId is required'
        },
        { status: 400 }
      );
    }

    if (ranges && Array.isArray(ranges)) {
      // Get multiple ranges at once
      const data = await GoogleSheetsService.getMultipleRanges(spreadsheetId, ranges);
      return NextResponse.json({
        success: true,
        data: data
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request body. Expected "spreadsheetId" and "ranges" array.'
        },
        { status: 400 }
      );
    }

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
