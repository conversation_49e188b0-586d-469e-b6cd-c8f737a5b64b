export const FILTER_TYPES = {
  ALL: 'all',
  LIVE: 'live',
  HOT: 'hot',
  TODAY: 'today',
  TOMORROW: 'tomorrow'
} as const;

export const FILTER_LABELS = {
  [FILTER_TYPES.ALL]: 'Tất cả',
  [FILTER_TYPES.LIVE]: 'Trực tiếp',
  [FILTER_TYPES.HOT]: 'Trận hot',
  [FILTER_TYPES.TODAY]: 'Hôm nay',
  [FILTER_TYPES.TOMORROW]: 'Ngày mai'
} as const;

export type FilterType = typeof FILTER_TYPES[keyof typeof FILTER_TYPES];
export type FilterLabel = typeof FILTER_LABELS[keyof typeof FILTER_LABELS];
