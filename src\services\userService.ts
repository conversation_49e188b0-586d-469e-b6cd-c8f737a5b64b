export interface UserInfo {
  uid: string;
  displayName: string;
  email?: string;
  photoURL?: string;
  [key: string]: unknown;
}

// Cache để lưu trữ kết quả
const userCache = new Map<string, UserInfo[]>();
const cacheExpiry = new Map<string, number>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 phút

export const getAllUsers = async (role: string, limit: number): Promise<UserInfo[] | null> => {
  const cacheKey = `${role}-${limit}`;
  const now = Date.now();
  
  // Kiểm tra cache còn hợp lệ không
  if (userCache.has(cacheKey) && cacheExpiry.has(cacheKey)) {
    if (now < cacheExpiry.get(cacheKey)!) {
      return userCache.get(cacheKey)!;
    }
  }

  const url = `/api/proxy/forum/search-users?&role=${role}&limit=${limit}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const userData = await response.json();
    let users: UserInfo[];

    if (Array.isArray(userData)) {
      users = userData;
    } else {
      users = userData.users || userData;
    }

    // Lưu vào cache
    userCache.set(cacheKey, users);
    cacheExpiry.set(cacheKey, now + CACHE_DURATION);

    return users;
  } catch (error) {
    // Failed to get all users
    return null;
  }
};

// Cache cho tất cả users theo role
const allUsersCache = new Map<string, UserInfo[]>();
const allUsersCacheExpiry = new Map<string, number>();

const getAllUsersByRole = async (role: string): Promise<UserInfo[]> => {
  const now = Date.now();
  
  // Kiểm tra cache
  if (allUsersCache.has(role) && allUsersCacheExpiry.has(role)) {
    if (now < allUsersCacheExpiry.get(role)!) {
      return allUsersCache.get(role)!;
    }
  }

  const users = await getAllUsers(role, 1000);
  const userList = users || [];
  
  // Lưu vào cache
  allUsersCache.set(role, userList);
  allUsersCacheExpiry.set(role, now + CACHE_DURATION);
  
  return userList;
};

const findUserInRoleLists = async (blvId: string): Promise<UserInfo | null> => {
  const roles = ["admin", "commentator"];
  
  // Fetch tất cả users từ các role cùng một lúc
  const userPromises = roles.map(role => getAllUsersByRole(role));
  
  try {
    const allRoleUsers = await Promise.all(userPromises);
    
    // Tìm kiếm user trong tất cả các role
    for (let i = 0; i < roles.length; i++) {
      const users = allRoleUsers[i];
      const foundUser = users.find((user: UserInfo) => user.uid === blvId);
      
      if (foundUser) {
        return foundUser;
      }
    }
    
    // Nếu không tìm thấy, trả về admin đầu tiên
    const adminUsers = allRoleUsers[0]; // admin users (index 0)
    return (adminUsers && adminUsers[0]) || null;
    
  } catch (error) {
    // Failed to search users
    
    // Fallback: thử lấy admin user
    try {
      const adminUsers = await getAllUsersByRole("admin");
      return (adminUsers && adminUsers[0]) || null;
    } catch (fallbackError) {
      // Failed to get fallback user
      return null;
    }
  }
};

// Thêm function để clear cache nếu cần
export const clearUserCache = () => {
  userCache.clear();
  cacheExpiry.clear();
  allUsersCache.clear();
  allUsersCacheExpiry.clear();
};

// Thêm function để invalidate cache của một role cụ thể
export const invalidateRoleCache = (role: string) => {
  const keysToDelete = Array.from(userCache.keys()).filter(key => key.startsWith(`${role}-`));
  keysToDelete.forEach(key => {
    userCache.delete(key);
    cacheExpiry.delete(key);
  });
  
  allUsersCache.delete(role);
  allUsersCacheExpiry.delete(role);
};

export const getUserInfo = findUserInRoleLists;