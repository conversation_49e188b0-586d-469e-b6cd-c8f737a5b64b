"use client";

interface MatchCardSkeletonProps {
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  style?: React.CSSProperties;
}

export default function MatchCardSkeleton({ 
  variant = 'detailed', 
  className = "",
  style
}: MatchCardSkeletonProps) {
  const renderCompactSkeleton = () => (
    <div className="p-2 sm:p-2 lg:p-3">
      {/* Competition Header */}
      <div className="flex items-center justify-between mb-2 lg:mb-3">
        <div className="flex items-center gap-1 sm:gap-2">
          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-gray-300 dark:bg-gray-600 rounded-sm skeleton-shimmer"></div>
          <div className="w-16 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
        <div className="flex flex-col items-end gap-1">
          <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
          <div className="w-12 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
      </div>

      {/* Teams and Score */}
      <div className="flex items-center justify-between mb-2 lg:mb-3">
        {/* Home Team */}
        <div className="flex items-center gap-1 sm:gap-2 flex-1 min-w-0">
          <div className="w-20 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
          <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
        </div>

        {/* Score */}
        <div className="flex items-center gap-1 mx-1 lg:mx-2">
          <div className="w-6 h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
          <div className="w-2 h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
          <div className="w-6 h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>

        {/* Away Team */}
        <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-end min-w-0">
          <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
          <div className="w-20 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
      </div>

      {/* Divider */}
      <div className="border-t border-gray-200 dark:border-gray-700 mb-2 lg:mb-3"></div>

      {/* BLV Section */}
      <div className="flex flex-col items-center gap-1 lg:gap-1.5 mb-2 lg:mb-3">
        <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
        <div className="w-12 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1 sm:gap-1.5">
          <div className="w-8 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
        <div className="w-10 h-8 sm:w-4 sm:h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
      </div>
    </div>
  );

  const renderDetailedSkeleton = () => (
    <div className="p-2 lg:p-4">
      {/* Competition Header with Notch */}
      <div className="relative mb-2 lg:mb-4">
        <div className="relative h-8 flex items-center justify-center">
          {/* Notch skeleton */}
          <div className="w-32 h-8 bg-gray-300 dark:bg-gray-600 rounded-t-lg skeleton-shimmer"></div>
          
          {/* Status text skeleton */}
          <div className="absolute top-1/2 left-1/2 w-16 h-4 bg-gray-400 dark:bg-gray-500 rounded skeleton-shimmer"></div>

          {/* Left side - Competition */}
          <div className="absolute left-0 top-0 w-20 h-6 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>

          {/* Right side - Time */}
          <div className="absolute right-0 top-0 w-16 h-6 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
        </div>
      </div>

      {/* Teams and Score */}
      <div className="flex items-center justify-center gap-6 mb-2 lg:mb-3">
        {/* Home Team */}
        <div className="flex items-center gap-3 min-w-0">
          <div className="w-32 h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
        </div>

        {/* Score */}
        <div className="w-20 h-8 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>

        {/* Away Team */}
        <div className="flex items-center gap-3 min-w-0">
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
          <div className="w-32 h-4 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
      </div>

      {/* Divider */}
      <div className="border-t border-gray-200 dark:border-gray-700 mb-2 lg:mb-3"></div>

      {/* BLV Section */}
      <div className="flex flex-col items-center gap-1 lg:gap-1.5 mb-2 lg:mb-3">
        <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full skeleton-shimmer"></div>
        <div className="w-16 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1.5 lg:gap-2">
          <div className="w-12 h-3 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
        </div>
        <div className="w-10 h-8 bg-gray-300 dark:bg-gray-600 rounded skeleton-shimmer"></div>
      </div>
    </div>
  );

  const cardContent = variant === 'compact' ? renderCompactSkeleton() : renderDetailedSkeleton();

  return (
    <div
      className={`relative rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-hidden ${className}`}
      style={{
        backgroundImage: 'url(/bg/bg-card.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        ...style
      }}
    >
      {/* Overlay for better skeleton visibility */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-800/60 z-0"></div>
      
      {cardContent}
    </div>
  );
}
