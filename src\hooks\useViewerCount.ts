import { useState, useEffect, useRef, useCallback } from "react";

interface UseViewerCountOptions {
  baseCount?: number;
  minVariation?: number;
  maxVariation?: number;
  minInterval?: number;
  maxInterval?: number;
}

const getRandomInitialCount = (
  min: number = 5000,
  max: number = 7000
): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

export function useViewerCount(options: UseViewerCountOptions = {}) {
  const {
    baseCount = getRandomInitialCount(),
    minVariation = -30,
    maxVariation = 30,
    minInterval = 30000,
    maxInterval = 60000,
  } = options;

  const [viewerCount, setViewerCount] = useState(baseCount);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const formatViewerCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(2)}K`;
    }
    return count.toString();
  };

  const getRandomInterval = useCallback(() => {
    return (
      Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval
    );
  }, [maxInterval, minInterval]);

  const getRandomVariation = useCallback(() => {
    return (
      Math.floor(Math.random() * (maxVariation - minVariation + 1)) +
      minVariation
    );
  }, [maxVariation, minVariation]);

  const updateViewerCount = useCallback(() => {
    setViewerCount((prevCount) => {
      const variation = getRandomVariation();
      const newCount = Math.max(1000, prevCount + variation); // Ensure minimum of 1000 viewers
      return newCount;
    });
  }, [getRandomVariation]);

  const scheduleNextUpdate = useCallback(() => {
    const nextInterval = getRandomInterval();
    timeoutRef.current = setTimeout(() => {
      updateViewerCount();
      scheduleNextUpdate();
    }, nextInterval);
  }, [getRandomInterval, updateViewerCount]);

  useEffect(() => {
    // Start the first update after a random initial delay
    const initialDelay = getRandomInterval();
    timeoutRef.current = setTimeout(() => {
      updateViewerCount();
      scheduleNextUpdate();
    }, initialDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [getRandomInterval, updateViewerCount, scheduleNextUpdate]);

  return {
    viewerCount,
    formattedViewerCount: formatViewerCount(viewerCount),
  };
}
