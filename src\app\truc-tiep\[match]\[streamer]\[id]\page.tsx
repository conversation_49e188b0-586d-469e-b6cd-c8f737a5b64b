"use client";

import LiveMatchLayout from "@/components/features/live/LiveMatchLayout";
import { useDetection, useMobile } from "@/hooks";
import useLiveMatch from "@/hooks/useLiveMatch";

type LiveDetailRefactoredParams = {
  match?: string;
  streamer?: string;
  id?: string;
};

export default function LiveDetailRefactored({
  params,
}: {
  params: Promise<LiveDetailRefactoredParams>;
}) {
  const {
    // States
    isLoggedIn,
    videoUrl,
    matchData,
    blvInfo,
    loading,
    matchId,
    // streamerName,
    matchSlug,
    // Handlers
  } = useLiveMatch(params);

  const { isMobile } = useDetection();

  return (
    <LiveMatchLayout
      videoUrl={videoUrl}
      matchData={matchData}
      blvInfo={blvInfo}
      loading={loading}
      isLoggedIn={isLoggedIn}
      onOpenAuthModal={() => {}}
      matchId={matchId}
      matchSlug={matchSlug}
      isMobile={isMobile}
    />
  );
}
