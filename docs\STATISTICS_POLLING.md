# Statistics Real-time Polling Implementation

## Tổng quan

Hệ thống này implement việc cập nhật thống kê trận đấu (StatisticsData) theo thời gian thực với các tối ưu hóa sau:

- **Polling mỗi 3 giây** khi trận đấu đang diễn ra
- **Deep comparison** để chỉ re-render khi có thay đổi thực sự
- **Memoization** để tránh tính toán không cần thiết
- **Error handling** và cleanup tự động

## Cấu trúc Files

```
src/
├── hooks/
│   └── useMatchStatistics.ts     # Custom hook quản lý polling
├── components/
│   ├── features/
│   │   └── MatchStatistics.tsx   # Component chính đã được refactor
│   └── demo/
│       └── StatisticsDemo.tsx    # Demo component để test
└── docs/
    └── STATISTICS_POLLING.md     # Tài liệu này
```

## Custom Hook: useMatchStatistics

### Cách sử dụng

```typescript
import { useMatchStatistics } from '@/hooks/useMatchStatistics';

const { matchStats, isPolling, error, refreshStats } = useMatchStatistics(matchData, {
  pollingInterval: 3000,    // Polling mỗi 3 giây (mặc định)
  enablePolling: true,      // Bật/tắt polling (mặc định: true)
});
```

### Tham số

- `matchData`: MatchData | null - Dữ liệu trận đấu
- `options`: UseMatchStatisticsOptions
  - `pollingInterval`: number - Khoảng thời gian polling (ms)
  - `enablePolling`: boolean - Bật/tắt polling

### Trả về

- `matchStats`: StatisticsData | null - Dữ liệu thống kê đã xử lý
- `isPolling`: boolean - Trạng thái polling
- `error`: string | null - Lỗi nếu có
- `refreshStats`: () => void - Function để refresh thủ công

## Tính năng chính

### 1. Automatic Polling

Hook tự động bật polling khi:
- `matchData.status` là `'live'`, `'in_progress'`, `'LIVE'`, hoặc `'IN_PROGRESS'`
- `enablePolling` là `true`

```typescript
// Polling sẽ tự động bật
const matchData = {
  id: "match-123",
  status: "live",
  // ... other data
};
```

### 2. Deep Comparison

Chỉ update state khi có thay đổi thực sự:

```typescript
const deepEqual = (obj1: StatisticsData | null, obj2: StatisticsData | null): boolean => {
  // So sánh từng field của statistics
  // Chỉ return false khi có thay đổi thực sự
};
```

### 3. Error Handling

- Xử lý lỗi API một cách graceful
- Không làm crash UI
- Hiển thị thông báo lỗi cho user

### 4. Performance Optimizations

- **useMemo** cho displayStats và statsData
- **useCallback** cho các functions
- **useRef** để lưu trữ previous state
- Cleanup interval khi component unmount

## Component Usage

### MatchStatistics Component

```typescript
import MatchStatistics from '@/components/features/MatchStatistics';

<MatchStatistics 
  matchData={matchData} 
  isLoading={false}
/>
```

Component sẽ:
- Hiển thị indicator khi đang polling
- Hiển thị thông báo lỗi nếu có
- Tự động cập nhật dữ liệu mỗi 3 giây cho live matches

### Demo Component

Để test functionality:

```typescript
import StatisticsDemo from '@/components/demo/StatisticsDemo';

<StatisticsDemo />
```

Demo cho phép:
- Chuyển đổi giữa trạng thái live/finished
- Bật/tắt loading state
- Xem polling hoạt động real-time

## API Integration

Hook sử dụng `fetchMatchStatistics` từ `@/services/matchService`:

```typescript
const statisticsData = await fetchMatchStatistics(matchData.id);
```

API trả về `StatisticsResponse[]` được convert thành `StatisticsData`.

## Cấu hình Match Status

Polling sẽ hoạt động khi match có status:
- `'live'`
- `'in_progress'` 
- `'LIVE'`
- `'IN_PROGRESS'`

Để tắt polling, set status khác (ví dụ: `'finished'`, `'not_started'`).

## Best Practices

### 1. Sử dụng với React.memo

```typescript
import { memo } from 'react';

const MatchStatistics = memo(({ matchData, isLoading }) => {
  // Component logic
});
```

### 2. Cleanup khi không cần

```typescript
const { matchStats } = useMatchStatistics(matchData, {
  enablePolling: matchData?.status === 'live', // Chỉ bật khi cần
});
```

### 3. Handle loading states

```typescript
<MatchStatistics 
  matchData={matchData} 
  isLoading={isLoadingMatch} // Tắt polling khi đang load match data
/>
```

## Troubleshooting

### Polling không hoạt động

1. Kiểm tra `matchData.status` có đúng format không
2. Kiểm tra `enablePolling` có được set `true` không
3. Kiểm tra `matchData.id` có tồn tại không

### Performance issues

1. Sử dụng `React.memo` cho component
2. Giảm `pollingInterval` nếu cần
3. Kiểm tra deep comparison logic

### API errors

1. Kiểm tra network connection
2. Kiểm tra API endpoint
3. Xem console logs để debug

## Ví dụ hoàn chỉnh

```typescript
import { useMatchStatistics } from '@/hooks/useMatchStatistics';
import { MatchData } from '@/types/match';

interface MyComponentProps {
  matchData: MatchData | null;
}

const MyComponent = ({ matchData }: MyComponentProps) => {
  const { matchStats, isPolling, error, refreshStats } = useMatchStatistics(matchData, {
    pollingInterval: 3000,
    enablePolling: matchData?.status === 'live',
  });

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      {isPolling && <div>🔄 Đang cập nhật...</div>}
      
      <div>
        Possession: {matchStats?.possession?.home}% - {matchStats?.possession?.away}%
      </div>
      
      <button onClick={refreshStats}>
        Refresh Manual
      </button>
    </div>
  );
};
```

## Kết luận

Implementation này cung cấp:
- ✅ Real-time updates mỗi 3 giây
- ✅ Chỉ re-render khi có thay đổi
- ✅ Performance optimizations
- ✅ Error handling
- ✅ Easy to use và maintain
- ✅ Configurable và extensible
