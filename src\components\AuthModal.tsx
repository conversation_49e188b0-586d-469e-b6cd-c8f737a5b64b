"use client";

import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "./ThemeProvider";
import type { User } from "@supabase/supabase-js";

interface UserData {
  email: string;
  name: string;
  isLoggedIn: boolean;
}

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'register';
  onModeChange?: (mode: 'login' | 'register') => void;
  onLoginSuccess?: (userData: UserData) => void;
}

export default function AuthModal({ isOpen, onClose, initialMode = 'login', onModeChange, onLoginSuccess }: AuthModalProps) {
  const [isLogin, setIsLogin] = useState(initialMode === 'login');
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasProcessedLogin = useRef(false);

  const { signIn, signUp, signInWithGoogle, user, profile } = useAuth();
  const { theme } = useTheme();

  // Đồng bộ state khi initialMode thay đổi
  useEffect(() => {
    setIsLogin(initialMode === 'login');
  }, [initialMode]);

  // Xử lý khi user đăng nhập thành công - chỉ khi modal đang mở và chưa xử lý
  useEffect(() => {
    if (user && profile && onLoginSuccess && isOpen && !isLoading && !hasProcessedLogin.current) {
      hasProcessedLogin.current = true;
      const userData: UserData = {
        email: user.email || '',
        name: profile.full_name || user.user_metadata?.full_name || '',
        isLoggedIn: true
      };
      onLoginSuccess(userData);
      // Không cần lưu vào localStorage nữa vì Header sử dụng AuthContext
      onClose();
      setFormData({ firstName: "", lastName: "", email: "", password: "" });
      setError(null);
    }
  }, [user, profile, onLoginSuccess, onClose, isOpen, isLoading]);

  useEffect(() => {
    if (!isOpen) {
      hasProcessedLogin.current = false;
    }
  }, [isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (isLogin) {
        const { error } = await signIn({
          email: formData.email,
          password: formData.password,
        });

        if (error) {
          setError(error.message);
        } else {
        }
      } else {
        const { user, error } = await signUp({
          email: formData.email,
          password: formData.password,
          fullName: `${formData.firstName} ${formData.lastName}`.trim(),
          role: 'member',
        });

        if (error) {
          setError(error.message);
        } else {
          setError(null);
          alert('Đăng ký thành công! Bạn có thể đăng nhập ngay bây giờ.');
          
          setIsLogin(true);
          setFormData({ firstName: "", lastName: "", email: formData.email, password: "" });
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { error } = await signInWithGoogle();
      if (error) {
        setError(error.message);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Đăng nhập Google thất bại';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    const newMode = !isLogin ? 'login' : 'register';
    setIsLogin(!isLogin);
    setFormData({ firstName: "", lastName: "", email: "", password: "" });
    setError(null);
    
    if (onModeChange) {
      onModeChange(newMode);
    }
  };

  if (!isOpen) {
    return null;
  }
  
  return (
    <div 
      className={`fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-[99999] p-4 ${theme === 'dark' ? 'dark' : ''}`}
      style={{ 
        position: 'fixed', 
        top: 0, 
        left: 0, 
        right: 0, 
        bottom: 0, 
        zIndex: 99999,
        backgroundColor: theme === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }}
    >
      <div 
        className="bg-white dark:bg-gray-900/95 dark:backdrop-blur-md rounded-xl shadow-2xl w-full max-w-md mx-auto relative z-[100000] border border-gray-200 dark:border-gray-700"
        style={{
          backgroundColor: theme === 'dark' ? 'rgba(17, 24, 39, 0.95)' : 'white',
          backdropFilter: theme === 'dark' ? 'blur(12px)' : 'none',
          borderRadius: '12px',
          boxShadow: theme === 'dark' 
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)' 
            : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          width: '100%',
          maxWidth: '448px',
          position: 'relative',
          zIndex: 100000,
          border: theme === 'dark' ? '1px solid rgba(55, 65, 81, 0.7)' : '1px solid rgba(229, 231, 235, 1)'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isLogin ? "Đăng nhập" : "Đăng ký"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-all duration-300 hover:rotate-90 group p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg className="w-6 h-6 transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          {/* First Name & Last Name (Register only) */}
          {!isLogin && (
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Họ
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${theme === 'dark' ? 'placeholder-gray-400' : 'placeholder-gray-500'}`}
                  style={{
                    backgroundColor: theme === 'dark' ? 'transparent' : 'white',
                    backdropFilter: theme === 'dark' ? 'blur(4px)' : 'none',
                    border: theme === 'dark' ? '1px solid rgba(75, 85, 99, 1)' : '1px solid rgba(209, 213, 219, 1)',
                    color: theme === 'dark' ? 'white' : 'rgb(17, 24, 39)',
                  }}
                  placeholder="Nhập họ"
                  required
                />
              </div>
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tên
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${theme === 'dark' ? 'placeholder-gray-400' : 'placeholder-gray-500'}`}
                  style={{
                    backgroundColor: theme === 'dark' ? 'transparent' : 'white',
                    backdropFilter: theme === 'dark' ? 'blur(4px)' : 'none',
                    border: theme === 'dark' ? '1px solid rgba(75, 85, 99, 1)' : '1px solid rgba(209, 213, 219, 1)',
                    color: theme === 'dark' ? 'white' : 'rgb(17, 24, 39)',
                  }}
                  placeholder="Nhập tên"
                  required
                />
              </div>
            </div>
          )}

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              style={{
                backgroundColor: theme === 'dark' ? 'transparent' : 'white',
                backdropFilter: theme === 'dark' ? 'blur(4px)' : 'none',
                border: theme === 'dark' ? '1px solid rgba(75, 85, 99, 1)' : '1px solid rgba(209, 213, 219, 1)',
                color: theme === 'dark' ? 'white' : 'rgb(17, 24, 39)'
              }}
              placeholder="Nhập email"
              required
            />
          </div>

          {/* Password */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Mật khẩu
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              style={{
                backgroundColor: theme === 'dark' ? 'transparent' : 'white',
                backdropFilter: theme === 'dark' ? 'blur(4px)' : 'none',
                border: theme === 'dark' ? '1px solid rgba(75, 85, 99, 1)' : '1px solid rgba(209, 213, 219, 1)',
                color: theme === 'dark' ? 'white' : 'rgb(17, 24, 39)'
              }}
              placeholder="Nhập mật khẩu"
              required
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 dark:bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-blue-400 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
          >
            {isLoading && (
              <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {isLoading ? (isLogin ? "Đang đăng nhập..." : "Đang đăng ký...") : (isLogin ? "Đăng nhập" : "Đăng ký")}
          </button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white dark:bg-gray-900/95 text-gray-500 dark:text-gray-400">hoặc</span>
            </div>
          </div>

          {/* Google Auth Button */}
          <button
            type="button"
            onClick={handleGoogleAuth}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-3 bg-white dark:bg-transparent dark:backdrop-blur-sm border border-gray-300 dark:border-gray-500 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md"
          >
            {/* Google Icon SVG */}
            <svg className="w-4 h-4" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Đăng nhập bằng Google
          </button>

          {/* Toggle Mode */}
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            {isLogin ? (
              <>
                Chưa có tài khoản?{" "}
                <button
                  type="button"
                  onClick={toggleMode}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                  Đăng ký ngay
                </button>
              </>
            ) : (
              <>
                Đã có tài khoản?{" "}
                <button
                  type="button"
                  onClick={toggleMode}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                  Đăng nhập
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}