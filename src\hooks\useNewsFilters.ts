import { useState, useCallback, useMemo } from 'react';
import type { PostFilters, PostsQueryParams } from '@/types/news.types';

export interface UseNewsFiltersReturn {
  // State
  filters: PostFilters;
  searchQuery: string;
  sortBy: 'created_at' | 'updated_at' | 'published_at' | 'title';
  sortOrder: 'asc' | 'desc';
  currentPage: number;
  pageSize: number;
  
  // Actions
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<PostFilters>) => void;
  setSortBy: (sortBy: 'created_at' | 'updated_at' | 'published_at' | 'title') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  resetFilters: () => void;
  
  // Computed
  queryParams: PostsQueryParams;
}

const DEFAULT_FILTERS: PostFilters = {
  published: true, // Chỉ hiển thị bài viết đã xuất bản
};

const DEFAULT_SORT_BY = 'published_at';
const DEFAULT_SORT_ORDER = 'desc';
const DEFAULT_PAGE_SIZE = 12;

export function useNewsFilters(initialFilters?: Partial<PostFilters>): UseNewsFiltersReturn {
  const [filters, setFiltersState] = useState<PostFilters>({
    ...DEFAULT_FILTERS,
    ...initialFilters
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'published_at' | 'title'>(DEFAULT_SORT_BY);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(DEFAULT_SORT_ORDER);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const setFilters = useCallback((newFilters: Partial<PostFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset về trang đầu khi thay đổi filter
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState(DEFAULT_FILTERS);
    setSearchQuery('');
    setSortBy(DEFAULT_SORT_BY);
    setSortOrder(DEFAULT_SORT_ORDER);
    setCurrentPage(1);
    setPageSize(DEFAULT_PAGE_SIZE);
  }, []);

  const queryParams = useMemo((): PostsQueryParams => {
    const params: PostsQueryParams = {
      ...filters,
      sort_by: sortBy,
      sort_order: sortOrder,
      page: currentPage,
      limit: pageSize,
      offset: (currentPage - 1) * pageSize
    };

    if (searchQuery.trim()) {
      params.search = searchQuery.trim();
    }

    return params;
  }, [filters, searchQuery, sortBy, sortOrder, currentPage, pageSize]);

  return {
    filters,
    searchQuery,
    sortBy,
    sortOrder,
    currentPage,
    pageSize,
    setSearchQuery,
    setFilters,
    setSortBy,
    setSortOrder,
    setCurrentPage,
    setPageSize,
    resetFilters,
    queryParams
  };
}

export default useNewsFilters;