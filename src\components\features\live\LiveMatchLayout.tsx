"use client";

import { MobileActionButtons } from "@/app/truc-tiep/[match]/[streamer]/[id]/MobileComponents";
import FloatingContact from "@/components/common/FloatingContact";
import VideoPlayer from "@/components/common/VideoPlayer";
import ChatTab from "@/components/features/live/ChatTab";
import RightSidebarTabNavigation from "@/components/features/live/RightSidebarTabNavigation";
import RelatedMatchesTab from "@/components/features/RelatedMatchesTab";
import { TAB_INDICES } from "@/constants/tabs";
import { formatTime, getStatusText } from "@/lib/utils";
import { UserInfo } from "@/services/userService";
import { MatchData } from "@/types/match";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface LiveMatchLayoutProps {
  // Video props
  videoUrl: string;

  // Match data
  matchData: MatchData | null;
  blvInfo: UserInfo | null;
  loading: boolean;

  // Auth props
  isLoggedIn: boolean;
  onOpenAuthModal: () => void;

  // Params
  matchId: string;
  matchSlug: string;

  // Responsive
  isMobile: boolean;
}

export default function LiveMatchLayout({
  videoUrl,
  matchData,
  blvInfo,
  loading,
  isLoggedIn,
  onOpenAuthModal,
  matchId,
  // matchSlug,
  isMobile,
}: LiveMatchLayoutProps) {
  const [activeTab, setActiveTab] = useState(1);
  const router = useRouter();

  const handleTabChange = (tab: number) => {
    if (tab === TAB_INDICES.HOME) {
      router.push(`/`);
    } else {
      setActiveTab(tab);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-custom-dark flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="text-gray-600 dark:text-gray-400">
            Đang tải thông tin trận đấu...
          </span>
        </div>
      </div>
    );
  }

  if (!matchData) {
    return (
      <div className="min-h-screen bg-white dark:bg-custom-dark flex items-center justify-center">
        <div className="text-center text-red-600 dark:text-red-400">
          <p className="font-semibold text-lg">
            Không thể tải thông tin trận đấu
          </p>
          <p className="text-sm mt-2">Vui lòng thử lại sau</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isMobile ? "h-screen flex flex-col" : "min-h-screen"}`}>
      <div className={`${isMobile ? "h-full flex flex-col" : "mt-4 mx-auto"}`}>
        {/* Match Header */}

        {/* Main Content - Video & Chat */}
        <div
          className={`${
            isMobile
              ? "h-full flex flex-col gap-4"
              : "grid gap-6 mb-6 grid-cols-1 lg:grid-cols-3"
          }`}
        >
          {/* Video Section */}
          <div
            className={`${
              isMobile ? "flex-shrink-0" : "col-span-1 lg:col-span-2"
            }`}
            style={isMobile ? { height: "auto" } : {}}
          >
            {/* Match Info */}
            <div className="hidden lg:block bg-white dark:bg-custom-dark rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-3 mb-2 relative z-10">
              <div className="w-full flex flex-wrap text-sm lg:text-lg items-center p-1 font-bold break-words gap-1">
                <div className="h-7 w-2 rounded-[10px] bg-gradient-to-r from-[#1E40AF] to-[#3B82F6] flex-shrink-0"></div>
                <div className="text-center">
                  <span className="">{matchData?.league}</span>
                  <span> - </span>
                </div>
                <div className="flex flex-wrap gap-1">
                  <div className="home_info flex items-center gap-2">
                    {matchData?.homeTeam?.name || "Team 1"}
                  </div>
                  <div>VS</div>
                  <div className="away_info flex items-center gap-2">
                    {matchData?.awayTeam?.name || "Team 2"}
                  </div>
                </div>
                <span> - </span>
                <span>{getStatusText(matchData?.status) || "Status"}</span>
                <span> - </span>
                <span>{formatTime(matchData?.time || "") || "Date"}</span>
              </div>
            </div>
            {/* Video Player */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-700">
              {/* Video Player */}
              <div className="relative">
                <VideoPlayer
                  videoUrl={videoUrl}
                  autoPlay={true}
                  muted={true}
                  volume={0.7}
                  theme="#0f1214"
                  className="w-full"
                  breakOutContainer={false}
                />
              </div>
            </div>
            <div className="hidden lg:block py-2">
              <div className="flex items-center justify-center gap-3 rounded py-1 text-sm lg:text-lg font-bold">
                <div className="home_info flex items-center gap-2">
                  {matchData?.homeTeam?.name || "Team 1"}
                  {matchData?.homeTeam?.logo && (
                    <img
                      alt={matchData.homeTeam.name}
                      loading="lazy"
                      width="35"
                      height="35"
                      decoding="async"
                      src={matchData.homeTeam.logo}
                      className="w-8 h-8 object-contain"
                    />
                  )}
                </div>
                <div className="score_info flex items-center gap-1">
                  <span className="text-primary text-xl font-semibold">
                    {matchData?.homeTeam?.score || 0}
                  </span>
                  <span className="font-semibold">-</span>
                  <span className="text-primary text-xl font-semibold">
                    {matchData?.awayTeam?.score || 0}
                  </span>
                </div>
                <div className="away_info flex items-center gap-2">
                  {matchData?.awayTeam?.logo && (
                    <img
                      alt={matchData.awayTeam.name}
                      loading="lazy"
                      width="35"
                      height="35"
                      decoding="async"
                      src={matchData.awayTeam.logo}
                      className="w-8 h-8 object-contain"
                    />
                  )}
                  {matchData?.awayTeam?.name || "Team 2"}
                </div>
              </div>
            </div>

            <div
              className={`border-t border-gray-200 dark:border-gray-700 ${
                isMobile ? "p-2 pb-0" : ""
              }`}
            >
              {/* Match Info Section with Modern Design */}
              <div className="w-full rounded pt-1" id="app-match-info">
                {/* Action Buttons Row - Mobile Only - Luôn hiển thị */}
                <MobileActionButtons blvInfo={blvInfo || {}} />

                {/* Main Match Info Card */}
                <div className="hidden sm:block mt-1 w-full shadow-[0px_0px_4px_4px_#1E40AF40] rounded border-2 p-2 border-blue-600 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 bg-cover bg-no-repeat opacity-30 rounded"></div>
                  <div className="relative z-10">
                    <div className="flex text-base w-full overflow-hidden">
                      <div className="flex flex-col lg:flex-row items-center w-full min-w-0">
                        {/* Left Section - BLV Info */}
                        <div className="flex items-start w-full lg:w-[60%] min-w-0 max-w-full">
                          <div className="flex flex-col gap-2 lg:gap-4 w-full min-w-0">
                            <div className="flex items-center min-w-0 w-full">
                              {/* Match Info with Gradient Design */}
                              <div className="flex gap-1 sm:gap-2 lg:gap-3 min-w-0 flex-1 overflow-hidden mx-2">
                                <div className="bg-gradient-to-l from-[#3B82F6] to-[#1E40AF] flex items-center rounded-bl-4xl rounded-tr-3xl p-0.5 px-1 lg:px-2 text-white -ml-2 sm:-ml-3 lg:-ml-4 min-w-0 flex-1 overflow-hidden">
                                  <div className="bg-white text-black font-bold p-1 px-1 sm:px-2 mx-1 ml-1 sm:ml-2 lg:ml-3 rounded-bl-xl rounded-tr-3xl min-w-0 w-16 sm:w-20 lg:w-auto flex-shrink-0">
                                    <div
                                      className="bg-gradient-to-l from-[#3B82F6] to-[#1E40AF] bg-clip-text text-transparent font-bold truncate text-xs lg:text-base"
                                      title={`BLV ${
                                        blvInfo?.displayName ||
                                        matchData?.liveData?.[0]?.blv
                                      }`}
                                    >
                                      {blvInfo?.displayName ||
                                        matchData?.liveData?.[0]?.blv}
                                    </div>
                                  </div>
                                  <div className="relative flex-1 min-w-0 overflow-hidden">
                                    <div className="flex items-center px-1 sm:px-2 gap-1 lg:gap-1 min-w-0 overflow-x-auto whitespace-nowrap scroll-smooth scrollbar-hide">
                                      <span
                                        className="text-xs lg:text-base font-semibold whitespace-nowrap lg:truncate"
                                        title={matchData?.homeTeam?.name}
                                      >
                                        {matchData?.homeTeam?.name}
                                      </span>
                                      <span className="text-sm lg:text-xl font-semibold flex-shrink-0">
                                        {matchData?.homeTeam?.score}
                                      </span>
                                      <span className="font-semibold flex-shrink-0 text-xs lg:text-base">
                                        -
                                      </span>
                                      <span className="text-sm lg:text-xl font-semibold flex-shrink-0">
                                        {matchData?.awayTeam?.score}
                                      </span>
                                      <span
                                        className="text-xs lg:text-base font-semibold whitespace-nowrap lg:truncate"
                                        title={matchData?.awayTeam?.name}
                                      >
                                        {matchData?.awayTeam?.name}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Right Section - Sponsor Logos & Follow Button */}
                        <div className="items-center hidden lg:flex w-full lg:w-[45%] h-full justify-end lg:justify-center lg:gap-5 mt-2 lg:mt-0 lg:pl-4">
                          <div className="flex items-center gap-3 border-2 rounded-lg h-full px-4">
                            <a
                              target="_blank"
                              className="lg:inline-flex hidden items-center justify-center h-12 lg:h-14 xl:h-16 min-w-20 xl:min-w-28"
                              href="https://ngoaihangtv.me/"
                            >
                              <Image
                                alt="KUDV Logo"
                                loading="lazy"
                                width={100}
                                height={60}
                                decoding="async"
                                className="object-contain h-8 lg:h-10 xl:h-12 w-auto max-w-none"
                                src="/vendor/ok-logo.png"
                              />
                            </a>
                          </div>
                          <div className="hidden lg:flex flex-col items-start flex-shrink-0 ml-2">
                            <div className="flex items-center text-xs xl:text-sm font-light whitespace-nowrap">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="17"
                                viewBox="0 0 16 17"
                                fill="none"
                              >
                                <path
                                  d="M8.55467 14.8668C10.6387 14.4495 13.3333 12.9501 13.3333 9.07347C13.3333 5.54613 10.7513 3.1968 8.89467 2.11747C8.482 1.87747 8 2.1928 8 2.66947V3.88813C8 4.84947 7.596 6.60413 6.47333 7.33413C5.9 7.7068 5.28 7.1488 5.21067 6.4688L5.15333 5.91013C5.08667 5.2608 4.42533 4.8668 3.90667 5.2628C2.974 5.9728 2 7.21947 2 9.0728C2 13.8135 5.526 14.9995 7.28867 14.9995C7.39178 14.9995 7.49933 14.9961 7.61133 14.9895C6.74067 14.9155 5.33333 14.3755 5.33333 12.6288C5.33333 11.2621 6.33 10.3388 7.08733 9.8888C7.29133 9.7688 7.52933 9.92547 7.52933 10.1621V10.5555C7.52933 10.8555 7.646 11.3255 7.92267 11.6468C8.236 12.0108 8.69533 11.6295 8.732 11.1508C8.744 11.0001 8.896 10.9041 9.02667 10.9801C9.454 11.2301 10 11.7635 10 12.6288C10 13.9941 9.24733 14.6221 8.55467 14.8668ZM3.5 8.25015C3.5 7.25559 3.89509 6.30176 4.59835 5.5985C5.30161 4.89524 6.25544 4.50015 7.25 4.50015H10.25C10.4489 4.50015 10.6397 4.57916 10.7803 4.71982C10.921 4.86047 11 5.05123 11 5.25015C11 5.44906 10.921 5.63982 10.7803 5.78048C10.6397 5.92113 10.4489 6.00015 10.25 6.00015H7.25C6.65326 6.00015 6.08097 6.2372 5.65901 6.65916C5.23705 7.08111 5 7.65341 5 8.25015V17.2501C5 17.8469 5.23705 18.4192 5.65901 18.8411C6.08097 19.2631 6.65326 19.5001 7.25 19.5001H16.25C16.8467 19.5001 17.419 19.2631 17.841 18.8411C18.2629 18.4192 18.5 17.8469 18.5 17.2501V15.7501C18.5 15.5512 18.579 15.3605 18.7197 15.2198C18.8603 15.0792 19.0511 15.0001 19.25 15.0001C19.4489 15.0001 19.6397 15.0792 19.7803 15.2198C19.921 15.3605 20 15.5512 20 15.7501V17.2501C20 18.2447 19.6049 19.1985 18.9017 19.9018C18.1984 20.6051 17.2446 21.0001 16.25 21.0001H7.25C6.25544 21.0001 5.30161 20.6051 4.59835 19.9018C3.89509 19.1985 3.5 18.2447 3.5 17.2501V8.25015Z"
                                  fill="url(#paint0_linear_2295_24237)"
                                ></path>
                                <defs>
                                  <linearGradient
                                    id="paint0_linear_2295_24237"
                                    x1="13.3333"
                                    y1="8.51731"
                                    x2="2"
                                    y2="8.51731"
                                    gradientUnits="userSpaceOnUse"
                                  >
                                    <stop stopColor="#3B82F6"></stop>
                                    <stop offset="1" stopColor="#1E40AF"></stop>
                                  </linearGradient>
                                </defs>
                              </svg>
                              <span
                                className="text-xs font-normal"
                                title="5226 người theo dõi"
                              >
                                5226 theo dõi
                              </span>
                            </div>
                            <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap cursor-pointer text-sm font-medium transition-colors focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground shadow-sm hover:bg-primary/90 py-2 h-7 rounded-full px-2 lg:h-8 bg-gradient-to-r from-[#1E40AF] to-[#3B82F6]">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="lucide lucide-circle-plus"
                              >
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M8 12h8"></path>
                                <path d="M12 8v8"></path>
                              </svg>
                              <span className="text-xs">Theo dõi</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Section */}
          <div
            className={`${
              isMobile
                ? "flex-1 flex flex-col min-h-0 overflow-hidden"
                : "col-span-1"
            }`}
          >
            <div
              className={`${
                isMobile ? "h-full flex flex-col" : ""
              } bg-white dark:bg-custom-dark rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-3 ${
                isMobile ? "mb-0" : "mb-2"
              } relative z-10 chat-section-container`}
            >
              <RightSidebarTabNavigation
                activeTab={activeTab}
                handleTabChange={handleTabChange}
                isMobile={isMobile}
              />

              <div
                className={`${
                  isMobile
                    ? "flex-1 min-h-0 overflow-hidden mb-20"
                    : "h-[600px] overflow-hidden"
                }`}
              >
                <ChatTab
                  isLoggedIn={isLoggedIn}
                  onOpenAuthModal={onOpenAuthModal}
                  matchId={matchId}
                  chatType="general"
                  activeTab={activeTab}
                  matchData={matchData}
                  onTabChange={handleTabChange}
                />
                <FloatingContact />
              </div>
            </div>
          </div>
        </div>

        {/* Related Matches Section */}
        <div className="hidden lg:block">
          <RelatedMatchesTab
            currentMatchId={matchId}
            currentCategory={matchData?.category || "football"}
          />
        </div>
      </div>
    </div>
  );
}
