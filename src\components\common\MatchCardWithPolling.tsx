import React, { memo } from 'react';
import { MatchData } from '@/types/match';
import MatchCard from './MatchCard';

interface MatchCardWithPollingProps {
  match: MatchData & { hasOrder?: boolean };
  variant?: 'compact' | 'detailed';
  className?: string;
  hasOrder?: boolean;
  style?: React.CSSProperties;
  // Polling related props (kept for compatibility but not used)
  isChanged?: boolean;
  forceUpdateTrigger?: number;
}

const MatchCardWithPolling: React.FC<MatchCardWithPollingProps> = memo(({
  match,
  variant = 'detailed',
  className = '',
  hasOrder = false,
  style,
  isChanged = false,
  forceUpdateTrigger = 0,
}) => {
  // Log when card updates (for debugging)
  if (isChanged && forceUpdateTrigger > 0) {
    console.log(`🔄 Updating card data for match ${match.id} - ${match.homeTeam.name} ${match.homeTeam.score}-${match.awayTeam.score} ${match.awayTeam.name}`);
  }

  // Just render the regular MatchCard with updated data
  return (
    <MatchCard
      match={match}
      variant={variant}
      className={className}
      hasOrder={hasOrder}
      style={style}
    />
  );
});

MatchCardWithPolling.displayName = 'MatchCardWithPolling';

export default MatchCardWithPolling;
