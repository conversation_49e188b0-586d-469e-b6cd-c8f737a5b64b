import React, { memo, useEffect, useState } from 'react';
import { MatchData } from '@/types/match';
import MatchCard from './MatchCard';

interface MatchCardWithPollingProps {
  match: MatchData & { hasOrder?: boolean };
  variant?: 'compact' | 'detailed';
  className?: string;
  hasOrder?: boolean;
  style?: React.CSSProperties;
  // Polling related props
  isChanged?: boolean;
  forceUpdateTrigger?: number;
}

const MatchCardWithPolling: React.FC<MatchCardWithPollingProps> = memo(({
  match,
  variant = 'detailed',
  className = '',
  hasOrder = false,
  style,
  isChanged = false,
  forceUpdateTrigger = 0,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationKey, setAnimationKey] = useState(0);

  // Handle animation when match changes
  useEffect(() => {
    if (isChanged && forceUpdateTrigger > 0) {
      console.log(`🎬 Animating card for match ${match.id} - trigger: ${forceUpdateTrigger}`);
      
      setIsAnimating(true);
      setAnimationKey(prev => prev + 1);
      
      // Remove animation class after animation completes
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 1000); // Animation duration
      
      return () => clearTimeout(timer);
    }
  }, [isChanged, forceUpdateTrigger, match.id]);

  const enhancedClassName = `
    ${className}
    ${isAnimating ? 'animate-match-update' : ''}
    ${isChanged ? 'ring-2 ring-blue-400 ring-opacity-75' : ''}
    transition-all duration-300
  `.trim();

  return (
    <div 
      key={`${match.id}-${animationKey}`}
      className="relative"
    >
      {/* Update indicator */}
      {isChanged && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="w-4 h-4 bg-blue-500 rounded-full animate-ping"></div>
          <div className="absolute top-0 right-0 w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        </div>
      )}
      
      {/* Flash overlay for animation */}
      {isAnimating && (
        <div className="absolute inset-0 bg-blue-200 dark:bg-blue-800 opacity-30 rounded-lg animate-flash z-5"></div>
      )}
      
      <MatchCard
        match={match}
        variant={variant}
        className={enhancedClassName}
        hasOrder={hasOrder}
        style={style}
      />
    </div>
  );
});

MatchCardWithPolling.displayName = 'MatchCardWithPolling';

export default MatchCardWithPolling;
