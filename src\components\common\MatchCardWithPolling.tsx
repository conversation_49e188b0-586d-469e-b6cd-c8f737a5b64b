import React, { memo } from 'react';
import { MatchData } from '@/types/match';
import MatchCard from './MatchCard';

interface MatchCardWithPollingProps {
  match: MatchData & { hasOrder?: boolean };
  variant?: 'compact' | 'detailed';
  className?: string;
  hasOrder?: boolean;
  style?: React.CSSProperties;
  // Polling related props (kept for compatibility but not used)
  isChanged?: boolean;
  forceUpdateTrigger?: number;
}

const MatchCardWithPolling: React.FC<MatchCardWithPollingProps> = memo(({
  match,
  variant = 'detailed',
  className = '',
  hasOrder = false,
  style,
  isChanged = false,
  forceUpdateTrigger = 0,
}) => {
  // Card will automatically re-render with new data when match prop changes
  // No logging needed here as it's already logged in the hook

  // Just render the regular MatchCard with updated data
  return (
    <MatchCard
      match={match}
      variant={variant}
      className={className}
      hasOrder={hasOrder}
      style={style}
    />
  );
});

MatchCardWithPolling.displayName = 'MatchCardWithPolling';

export default MatchCardWithPolling;
