{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3008 --turbopack", "build": "next build", "start": "next start -H 0.0.0.0 -p 3002", "lint": "bunx tsc --noEmit && next lint", "format": "bunx biome format --write"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/postgrest-js": "^1.21.4", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.0", "artplayer": "^5.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^158.0.0", "hls.js": "^1.6.9", "lucide-react": "^0.475.0", "next": "^15.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "same-runtime": "^0.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "swiper": "^11.2.10", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3.3.1", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "eslint": "^9.27.0", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}