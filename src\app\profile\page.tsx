"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Camera, ArrowLeft, Save } from "lucide-react"
import Link from "next/link"
import { ProfileService } from "@/services/profile.service"
import { createClient } from "@/lib/supabase/client"
import { toast } from "sonner"

const supabase = createClient()
const profileService = new ProfileService(supabase)

export default function EditProfilePage() {
  const [profileImage, setProfileImage] = useState<string>("")
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [fullName, setFullName] = useState("")
  const [loading, setLoading] = useState(false)

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImageFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }
  useEffect(() => {
    const fetchProfile = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { profile, error } = await profileService.getProfile(user.id)
      if (error) {
        return
      }

      if (profile) {
        setFullName(profile.full_name ?? "")
        setProfileImage(profile.avatar_url ?? "")
      }
    }

    fetchProfile()
  }, [])


const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault()

  try {
    setLoading(true)

    // Lấy user từ Supabase auth
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return
    }

    const userId = user.id
    let uploadedUrl = profileImage

    if (imageFile) {
      const { url, error } = await profileService.uploadAvatar(userId, imageFile)

      if (error) {
       toast.error("Upload avatar thất bại: " + error.message)
        return
      }

      uploadedUrl = url ?? ""
      setProfileImage(uploadedUrl)
    }

    // Gọi API update profile
    const { profile, error: updateError } = await profileService.updateProfile(userId, {
      full_name: fullName,
      avatar_url: uploadedUrl,
    })

    if (updateError) {
      toast.error("Cập nhật profile thất bại: " + updateError.message)
      return
    }
    toast.success("Cập nhật thành công")

  } catch (err) {
    toast.error("Cập nhật profile thất bại: " + err)
  } finally {
    setLoading(false)
  }
}


  return (
    <div className="bg-gray-50 dark:bg-custom-dark min-h-screen lg:py-10 py-4">
      <div className="px-4">
        <form
          onSubmit={handleSubmit}
          className="max-w-2xl mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg dark:shadow-gray-900/20 space-y-8"
        >
          {/* Header */}
          <div className="flex items-center gap-4 pb-4 border-b border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white">
            <Link href="/" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
              <ArrowLeft className="h-5 w-5 cursor-pointer" />
            </Link>
            <h2 className="lg:text-lg text-md font-semibold">Chỉnh sửa hồ sơ</h2>
          </div>

          {/* Body */}
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Avatar Upload */}
            <div className="flex flex-col items-center gap-3 lg:border-r lg:border-gray-200 dark:lg:border-gray-700 lg:pr-6">
              <Avatar className="h-28 w-28 border border-gray-200 dark:border-gray-600 shadow-md">
                <AvatarImage src={profileImage} alt="Avatar" />
                <AvatarFallback className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                  {fullName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <Input
                  type="file"
                  accept="image/*"
                  id="upload-avatar"
                  className="hidden"
                  onChange={handleImageUpload}
                />
                <Label
                  htmlFor="upload-avatar"
                  className="flex items-center gap-1 mt-2 justify-center cursor-pointer text-sm font-medium text-primary dark:text-blue-400 hover:text-primary/80 dark:hover:text-blue-300 transition-colors"
                >
                  <Camera className="h-4 w-4" />
                  Chọn ảnh đại diện
                </Label>
              </div>
            </div>

            {/* Full name field + actions */}
            <div className="flex-1 space-y-6">
              <div className="space-y-2">
                <Label htmlFor="full_name" className="text-gray-700 dark:text-gray-300 font-medium">
                  Họ và tên
                </Label>
                <Input
                  id="full_name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Nhập họ tên"
                  className="w-full bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-primary dark:focus:border-blue-400 focus:ring-primary dark:focus:ring-blue-400"
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700 pt-4">
                <Button 
                  type="button" 
                  variant="outline"
                  className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"
                >
                  Hủy
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700 text-white"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? "Đang lưu..." : "Lưu thay đổi"}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
