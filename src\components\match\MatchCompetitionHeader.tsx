"use client";

import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { formatTime, getStatusText } from "@/lib/utils";

import MatchTooltip from "../common/MatchTooltip";
import { StatisticsData } from "@/types/match";

interface MatchCompetitionHeaderProps {
  variant?: "default" | "compact" | "detailed";
  leagueName: string;
  matchStatus: string;
  matchTime: string;
  matchStats?: StatisticsData[];
  homeTeamName: string;
  awayTeamName: string;
  matchId: string;
  isMobile: boolean;
  showStatsPopup: boolean;
  onStatusHover: (e: React.MouseEvent) => void;
  onStatusLeave: () => void;
  onStatusClick: (e: React.MouseEvent) => void;
}

export default function MatchCompetitionHeader({
  variant = "default",
  leagueName,
  matchStatus,
  matchTime,
  matchStats = [],
  homeTeamName,
  awayTeamName,
  matchId,
  isMobile,
  showStatsPopup,
  onStatusHover,
  onStatusLeave,
  onStatusClick,
}: MatchCompetitionHeaderProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "live":
        return "bg-red-500 text-white";
      case "ht":
      case "halftime":
        return "bg-green-500 text-white";
      case "pending":
      case "sắp diễn ra":
      case "cuối tuần":
        return "bg-yellow-500 text-white";
      default:
        return "bg-blue-500 text-white";
    }
  };

  if (variant === "compact") {
    return (
      <div className="flex items-center justify-between mb-2 lg:mb-3 relative z-10">
        <div className="flex items-center gap-1 sm:gap-2">
          <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-blue-500 rounded-sm"></div>
          <span className="text-xs text-gray-900 dark:text-white font-medium truncate max-w-[70px] sm:max-w-[90px] lg:max-w-[120px]">
            {leagueName}
          </span>
        </div>
        <div className="flex flex-col items-end gap-1">
          <div
            className="relative"
            onMouseEnter={onStatusHover}
            onMouseLeave={onStatusLeave}
            onClick={onStatusClick}
            data-status-trigger
          >
            <span
              className={`inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium cursor-pointer hover:opacity-80 hover:scale-105 hover:shadow-lg transition-all duration-200 ${getStatusColor(
                matchStatus
              )}`}
            >
              {getStatusText(matchStatus)}
            </span>
          </div>
          <div className="text-xs text-gray-600 dark:text-custom-subtle">
            {formatTime(matchTime)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-2 lg:mb-4">
      <div className="relative h-8 flex items-center justify-center">
        <svg
          width="131"
          height="29"
          viewBox="0 0 131 29"
          fill="none"
          className="absolute top-0 left-1/2 -translate-x-1/2"
        >
          <foreignObject x="-19.333" y="-20" width="170" height="69">
            <div
              style={{
                backdropFilter: "blur(10px)",
                clipPath: "url(#bgblur_clip_path)",
                height: "100%",
                width: "100%",
              }}
            />
          </foreignObject>

          <g>
            <path
              d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
              fill="url(#paint0_linear)"
            />
            <path
              d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
              stroke="url(#paint1_linear)"
            />
            <path
              d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
              stroke="url(#paint2_linear)"
              strokeOpacity="0.2"
            />
          </g>

          {/* Definitions */}
          <defs>
            <clipPath id="bgblur_clip_path" transform="translate(19.333 20)">
              <path d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z" />
            </clipPath>

            {/* Gradients */}
            <linearGradient
              id="paint0_linear"
              x1="65.667"
              y1="29"
              x2="65.667"
              y2="0"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#00D962" />
              <stop offset="1" stopColor="#007334" />
            </linearGradient>
            <linearGradient
              id="paint1_linear"
              x1="65.667"
              y1="29"
              x2="65.667"
              y2="0"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#0A6027" />
              <stop offset="1" stopColor="#666666" stopOpacity="0" />
            </linearGradient>
            <linearGradient
              id="paint2_linear"
              x1="15.167"
              y1="25"
              x2="117.167"
              y2="25.5"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="white" />
              <stop offset="1" stopColor="white" />
            </linearGradient>
          </defs>
        </svg>
        {/* Status text inside notch */}
        {isMobile ? (
          // Mobile: Use click without Tooltip wrapper
          <div
            className="absolute top-1/2 left-1/2 max-w-[80%] -translate-x-1/2 -translate-y-1/2 z-10"
            onClick={onStatusClick}
            data-status-trigger
          >
            <span className="inline-block truncate text-xs whitespace-nowrap text-white lg:text-sm cursor-pointer hover:opacity-80 hover:scale-110 hover:shadow-lg transition-all duration-200">
              {getStatusText(matchStatus)}
            </span>
          </div>
        ) : (
          // Desktop: Use Tooltip with hover
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className="absolute top-1/2 left-1/2 max-w-[80%] -translate-x-1/2 -translate-y-1/2 z-10"
                onMouseEnter={onStatusHover}
                onMouseLeave={onStatusLeave}
                data-status-trigger
              >
                <span className="inline-block truncate text-xs whitespace-nowrap text-white lg:text-sm cursor-pointer hover:opacity-80 hover:scale-110 hover:shadow-lg transition-all duration-200">
                  {getStatusText(matchStatus)}
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="py-2 px-4" data-stats-popup>
                <MatchTooltip
                  matchStats={matchStats}
                  homeTeamName={homeTeamName}
                  awayTeamName={awayTeamName}
                  homeTeamId={matchId}
                  awayTeamId={matchId}
                  leagueName={leagueName}
                  matchStatus={matchStatus}
                />
              </div>
            </TooltipContent>
          </Tooltip>
        )}
        {/* Left side - Competition */}
        <div className="absolute left-0 top-0 flex items-center gap-1 rounded-full border border-[#FF6601] px-1 py-0.5 bg-white">
          <p className="w-[80px] truncate text-xs font-normal uppercase xl:w-[120px] text-gray-900 px-1">
            {leagueName}
          </p>
        </div>
        {/* Right side - Time */}
        <div className="absolute right-0 top-0">
          <span className="rounded-full border border-[#FF6601] bg-gradient-to-r from-[#F03131] to-[#FF6601] bg-clip-text px-2 py-1 text-xs font-semibold text-transparent lg:text-sm">
            {formatTime(matchTime)}
          </span>
        </div>
      </div>
    </div>
  );
}