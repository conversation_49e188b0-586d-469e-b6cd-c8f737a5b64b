import type {
  AuthResponse,
  LoginCredentials,
  Profile,
  ProfileResponse,
  RegisterData,
} from "@/types/auth.types";

import type { User, SupabaseClient } from "@supabase/supabase-js";

export class AuthService {
  private supabase: SupabaseClient;

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // Method để cập nhật supabase client từ context
  setSupabaseClient(client: SupabaseClient) {
    this.supabase = client;
  }

  async signIn(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signUp(registerData: RegisterData): Promise<AuthResponse> {
    try {
      const redirectUrl = `${process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL}/auth/callback`;
      const { data, error } = await this.supabase.auth.signUp({
        email: registerData.email,
        password: registerData.password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: registerData.fullName,
            role: registerData.role,
          },
        },
      });

      if (error && error.message.includes("Error sending confirmation email")) {
        if (data.user && "id" in data.user) {
          try {
            await this.createUserProfile((data.user as User).id, {
              full_name: registerData.fullName,
              role: registerData.role,
            });
          } catch (profileError) {}
        }
        return {
          user: data.user,
          error: null,
        };
      }

      if (data.user && "id" in data.user) {
        try {
          await this.createUserProfile((data.user as User).id, {
            full_name: registerData.fullName,
            role: registerData.role,
          });
        } catch (profileError) {
          // Profile creation failed
        }
      }

      if (data.user && error) {
        return {
          user: data.user,
          error: null,
        };
      }

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signInWithGoogle(): Promise<{ error: Error | null }> {
    try {
      const redirectUrl = `${process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL}/auth/callback`;

      const { error } = await this.supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: redirectUrl,
        },
      });

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signOut(): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase.auth.signOut();
      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getCurrentUser(): Promise<{ user: User | null; error: Error | null }> {
    try {
      const { data, error } = await this.supabase.auth.getUser();

      // Handle "Auth session missing" as a normal case, not an error
      if (error && error.message === "Auth session missing!") {
        return {
          user: null,
          error: null, // No error, just no session
        };
      }

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getProfile(userId: string): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async updateProfile(
    userId: string,
    updates: Partial<Profile>
  ): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .update(updates)
        .eq("id", userId)
        .select()
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  onAuthStateChange(
    callback: (event: string, session: { user: User | null } | null) => void
  ) {
    return this.supabase?.auth?.onAuthStateChange(callback);
  }

  async createUserProfile(
    userId: string,
    profileData: { full_name: string; role: string }
  ): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .insert({
          id: userId,
          full_name: profileData.full_name,
          role: profileData.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select();

      if (error) {
        throw error;
      }

      // Kiểm tra lại profile sau khi tạo để xem role có bị thay đổi không
      const { data: checkData, error: checkError } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (checkError) {
        // Error checking profile
      }
    } catch (error) {
      throw error;
    }
  }
}
