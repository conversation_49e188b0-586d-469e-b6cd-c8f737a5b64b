<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGOAIHANG TV - <PERSON>hông c<PERSON> kết n<PERSON>i mạng</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f1214 0%, #1a1d1f 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 25px;
            opacity: 0.8;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }
        
        .features {
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>Không có kết nối mạng</h1>
        <p>Vui lòng kiểm tra kết nối internet và thử lại. Một số tính năng có thể hoạt động offline.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Thử lại
        </button>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚽</div>
                <div>Lịch thi đấu</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div>Bảng xếp hạng</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📰</div>
                <div>Tin tức</div>
            </div>
        </div>
    </div>

    <script>
        // Check network status
        function updateOnlineStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        window.addEventListener('online', updateOnlineStatus);
        
        // Auto retry every 10 seconds
        setInterval(() => {
            if (navigator.onLine) {
                window.location.reload();
            }
        }, 10000);
    </script>
</body>
</html>
