"use client";

import { useSupabase } from "@/contexts/SupabaseContext";
import { useCallback, useEffect, useRef, useState } from "react";

interface TypingUser {
  id: string;
  name: string;
  avatar?: string;
}

interface UseTypingProps {
  channelId: string;
  currentUser: {
    id: string;
    full_name?: string;
    email: string;
    avatar_url?: string;
  };
}

export function useTyping({ channelId, currentUser }: UseTypingProps) {
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const { supabase } = useSupabase();
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const isTypingRef = useRef(false);

  const stopTyping = useCallback(() => {
    if (isTypingRef.current) {
      isTypingRef.current = false;
      supabase.channel(`typing:${channelId}`).send({
        type: "broadcast",
        event: "typing",
        payload: {
          user_id: currentUser.id,
          user_name: currentUser.full_name || currentUser.email,
          user_avatar: currentUser.avatar_url,
          is_typing: false,
        },
      });
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, [channelId, currentUser, supabase]);

  const startTyping = useCallback(() => {
    if (!isTypingRef.current) {
      isTypingRef.current = true;
      supabase.channel(`typing:${channelId}`).send({
        type: "broadcast",
        event: "typing",
        payload: {
          user_id: currentUser.id,
          user_name: currentUser.full_name || currentUser.email,
          user_avatar: currentUser.avatar_url,
          is_typing: true,
        },
      });
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [channelId, currentUser, supabase, stopTyping]);

  useEffect(() => {
    const channel = supabase
      .channel(`typing:${channelId}`)
      .on("broadcast", { event: "typing" }, (payload) => {
        const { user_id, is_typing, user_name, user_avatar } = payload.payload;

        if (user_id !== currentUser.id) {
          setTypingUsers((prev) => {
            if (is_typing) {
              const existingUser = prev.find((user) => user.id === user_id);
              if (!existingUser) {
                return [
                  ...prev,
                  {
                    id: user_id,
                    name: user_name,
                    avatar: user_avatar,
                  },
                ];
              }
              return prev;
            } else {
              return prev.filter((user) => user.id !== user_id);
            }
          });
        }
      })
      .subscribe();

    return () => {
      stopTyping();
      supabase.removeChannel(channel);
    };
  }, [channelId, currentUser.id, stopTyping, supabase]);

  return {
    typingUsers,
    startTyping,
    stopTyping,
  };
}
