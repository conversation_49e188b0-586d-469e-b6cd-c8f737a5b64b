"use client";

interface HeadToHeadTabProps {
  isLoading?: boolean;
}

export default function HeadToHeadTab({ isLoading = false }: HeadToHeadTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-2 p-2">
      {/* Overall Record Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Recent Matches Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Goals Scored Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Notable Matches Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Season Performance Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-2 p-2">
    </div>
  );
} 