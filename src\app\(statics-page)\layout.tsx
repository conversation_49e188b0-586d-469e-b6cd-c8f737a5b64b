"use client";

import { FileText, Info, LockKeyhole, Mail, ShieldAlert, TriangleAlert } from "lucide-react";

import Link from "next/link";
import { usePathname } from "next/navigation";

const menus = [
  { label: "<PERSON><PERSON>h sách bảo mật", href: "/chinh-sach-bao-mat", icon: LockKeyhole },
  { label: "<PERSON>ên hệ", href: "/lien-he", icon: Mail },
  { label: "Miễn trừ trách nhiệm", href: "/mien-tru-trach-nhiem", icon: TriangleAlert },
  { label: "<PERSON><PERSON><PERSON><PERSON> khoản dịch vụ", href: "/dieu-khoan-dich-vu", icon: FileText },
];

export default function LayoutStatics({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <div className="lg:mx-20 lg:my-6 lg:rounded-lg lg:border flex">
      <div className="lg:border-r lg:px-4 lg:py-10 hidden lg:block">
        <nav>
          <ul className="text-gray-600 flex flex-col gap-y-2">
            {menus.map((menu, index) => (
              <li key={index}>
                <Link
                  href={menu.href}
                  className={`flex items-center gap-1 p-2 rounded-md 
                    ${pathname === menu.href ? "bg-blue-100 text-blue-600" : "hover:text-blue-500"}
                  `}
                >
                  {menu.icon && <menu.icon size={18} />}
                  {menu.label}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      <div className="flex-1 p-6">{children}</div>
    </div>
  );
}
