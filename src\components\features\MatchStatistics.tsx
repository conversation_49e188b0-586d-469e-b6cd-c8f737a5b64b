"use client";

import { StatisticsData, StatisticsResponse, MatchData } from "@/types/match";
import { useEffect, useState } from "react";

interface MatchStatisticsProps {
  matchData: MatchData | null;
  isLoading?: boolean;
}

export default function MatchStatistics({ matchData, isLoading = false }: MatchStatisticsProps) {
  const [matchStats, setMatchStats] = useState<StatisticsData | null>(null);

  // Process statistics data similar to MatchTooltip
  useEffect(() => {
    const processStats = () => {
      if (!matchData?.statistics) {
        setMatchStats(null);
        return;
      }

      try {
        const statistics = matchData.statistics;
        
        if (Array.isArray(statistics) && statistics.length > 0) {
          // Find the first statistics group (full match)
          const fullMatchStats = statistics.find((stat: StatisticsResponse) => stat.type === 0) || statistics[0];
          
          if (fullMatchStats?.stats && fullMatchStats.stats.length >= 2) {
            const homeStats = fullMatchStats.stats[0];
            const awayStats = fullMatchStats.stats[1];

            const processedStats: StatisticsData = {
              possession: {
                home: homeStats.ball_possession || 0,
                away: awayStats.ball_possession || 0,
              },
              corners: {
                home: homeStats.corner_kicks || 0,
                away: awayStats.corner_kicks || 0,
              },
              goals: {
                home: homeStats.goals || 0,
                away: awayStats.goals || 0,
              },
              cards: {
                home: (homeStats.yellow_cards || 0) + (homeStats.red_cards || 0),
                away: (awayStats.yellow_cards || 0) + (awayStats.red_cards || 0),
              },
              shots: {
                home: homeStats.shots || 0,
                away: awayStats.shots || 0,
              },
              attacks: {
                home: homeStats.attacks || 0,
                away: awayStats.attacks || 0,
              },
              passes: {
                home: homeStats.passes || 0,
                away: awayStats.passes || 0,
              },
            };

            setMatchStats(processedStats);
          }
        }
      } catch (err) {
        // Failed to process statistics
        setMatchStats(null);
      }
    };

    processStats();
  }, [matchData]);

  // Use default stats if no data available
  const displayStats = matchStats || {
    possession: { home: 50, away: 50 },
    corners: { home: 0, away: 0 },
    goals: { home: 0, away: 0 },
    cards: { home: 0, away: 0 },
    shots: { home: 0, away: 0 },
    attacks: { home: 0, away: 0 },
    passes: { home: 0, away: 0 },
  };

  const getStatValue = (value: number, isPercentage = false) =>
    isPercentage ? `${value}%` : value.toString();

  const getStatBarWidth = (
    homeValue: number,
    awayValue: number,
    isPercentage = false
  ) => {
    const total = homeValue + awayValue;
    return total > 0 ? (homeValue / total) * 100 : 50;
  };

  const statsData = [
    {
      label: "Tỷ lệ kiểm soát bóng",
      homeValue: displayStats.possession?.home || 0,
      awayValue: displayStats.possession?.away || 0,
      isPercentage: true,
    },
    {
      label: "Phạt góc",
      homeValue: displayStats.corners?.home || 0,
      awayValue: displayStats.corners?.away || 0,
    },
    {
      label: "Bàn thắng",
      homeValue: displayStats.goals?.home || 0,
      awayValue: displayStats.goals?.away || 0,
    },
    {
      label: "Thẻ",
      homeValue: displayStats.cards?.home || 0,
      awayValue: displayStats.cards?.away || 0,
    },
    {
      label: "Sút bóng",
      homeValue: displayStats.shots?.home || 0,
      awayValue: displayStats.shots?.away || 0,
    },
    {
      label: "Tấn công",
      homeValue: displayStats.attacks?.home || 0,
      awayValue: displayStats.attacks?.away || 0,
    },
    {
      label: "Chuyền bóng",
      homeValue: displayStats.passes?.home || 0,
      awayValue: displayStats.passes?.away || 0,
    },
  ];

  if (isLoading) {
    return (
      <div className="h-full space-y-2 p-2">
        {/* Match Info Skeleton */}
        <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-3 animate-pulse"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="space-y-1">
                <div className="w-20 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Score Skeleton */}
        <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-3 animate-pulse"></div>
          <div className="flex items-center justify-center gap-4 mb-2">
            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="w-12 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </div>
          <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto"></div>
        </div>

        {/* Statistics Skeleton */}
        <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
          <div className="space-y-3">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="w-24 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                    <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full space-y-2 p-2">
      {/* Match Info */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          Thông tin trận đấu
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-gray-600 dark:text-gray-400">Giải đấu:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {matchData?.league || "Chưa xác định"}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Loại trận:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {matchData?.typeMatch || "Chưa xác định"}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Thời gian:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {matchData?.time || "Chưa xác định"}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Ngày:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {matchData?.date || "Chưa xác định"}
            </span>
          </div>
        </div>
      </div>

      {/* Score */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Tỷ số</h3>
        <div className="flex items-center justify-center gap-4 mb-2">
          <div className="text-center">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              {matchData?.homeTeam?.name || "Đội nhà"}
            </div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {matchData?.homeTeam?.score || 0}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">VS</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {matchData?.status || "Chưa bắt đầu"}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              {matchData?.awayTeam?.name || "Đội khách"}
            </div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {matchData?.awayTeam?.score || 0}
            </div>
          </div>
        </div>
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          <span>Trạng thái: {matchData?.status || "Chưa bắt đầu"}</span>
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Thống kê trận đấu
        </h3>
        
        <div className="space-y-3">
          {statsData.map((stat, index) => (
            <div key={index} className="space-y-2">
              {/* Values Row */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-900 dark:text-white font-medium w-16 text-right">
                  {getStatValue(stat.homeValue, stat.isPercentage)}
                </span>
                <span className="text-gray-600 dark:text-gray-400 text-center flex-1 px-2">
                  {stat.label}
                </span>
                <span className="text-gray-900 dark:text-white font-medium w-16 text-left">
                  {getStatValue(stat.awayValue, stat.isPercentage)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${getStatBarWidth(
                      stat.homeValue,
                      stat.awayValue
                    )}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
