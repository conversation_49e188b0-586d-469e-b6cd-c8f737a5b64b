"use client";

import { useEffect, useRef, useState, use } from "react";
import { useScreenLock } from "@/components/ScreenLockContext";
import { useAuth } from "@/contexts/AuthContext";
import { MatchData } from '@/types/match';
import { fetchMatchDetails } from '@/services/matchService';
import { getUserInfo, UserInfo } from '@/services/userService';

type Params = { match?: string; streamer?: string; id?: string };

interface UserData {
  email: string;
  name: string;
  isLoggedIn: boolean;
}

export default function useLiveDetailHook(params: Promise<Params>) {
  const resolvedParams = use(params);
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(1);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [videoUrl, setVideoUrl] = useState<string>('https://quantri.ngoaihangtv.xyz/wp-content/uploads/2025/04/0408.mp4');
  const [matchData, setMatchData] = useState<MatchData | null>(null);
  const [blvInfo, setBlvInfo] = useState<UserInfo | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [videoHeight, setVideoHeight] = useState(0);
  const [mobileViewportHeight, setMobileViewportHeight] = useState(0);
  const [videoTopPosition, setVideoTopPosition] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const [loading, setLoading] = useState(true);
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  
  const { isScreenLocked, setIsScreenLocked } = useScreenLock();
  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const videoRef = useRef<HTMLDivElement | null>(null);

  const matchId = resolvedParams.id || 'unknown';
  const streamerName = resolvedParams.streamer || 'a';
  const matchSlug = resolvedParams.match || 'match';

  const handleScroll = () => {
    if (chatContainerRef.current) {
    }
  };

  useEffect(() => {
    if ('history' in window) {
      const originalScrollRestoration = window.history.scrollRestoration;
      window.history.scrollRestoration = 'manual';

      const savedScrollY = window.scrollY;

      const preventAutoScroll = () => {
        if (window.scrollY !== savedScrollY) {
          window.scrollTo(0, savedScrollY);
        }
      };

      const timer = setTimeout(() => {
        window.removeEventListener('scroll', preventAutoScroll);
      }, 1000);

      window.addEventListener('scroll', preventAutoScroll);

      return () => {
        clearTimeout(timer);
        window.removeEventListener('scroll', preventAutoScroll);
        window.history.scrollRestoration = originalScrollRestoration;
      };
    }
  }, []);

  useEffect(() => {
    setIsClient(true);
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.width = '100%';
    document.body.style.maxWidth = '100%';

    const scrollY = window.scrollY;

    return () => {
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.width = '';
      document.body.style.maxWidth = '';
      window.scrollTo(0, scrollY);
    };
  }, []);

  // Sync isLoggedIn with user from context
  useEffect(() => {
    setIsLoggedIn(!!user);
  }, [user]);

  useEffect(() => {
    if (isClient) {
      const urlParams = new URLSearchParams(window.location.search);
      const videoParam = urlParams.get('video');
      const videosParam = urlParams.get('videos');

      if (videoParam) {
        setVideoUrl(videoParam);
      } else if (videosParam) {
        try {
          const videos = JSON.parse(videosParam);
          if (videos.length > 0) {
            setVideoUrl(videos[0]);
          }
        } catch (error) {
          // Error parsing videos parameter
        }
      }
    }
  }, [isClient]);

  useEffect(() => {
    const getMatchDetails = async () => {
      try {
        setLoading(true);
        const data = await fetchMatchDetails(matchId);

        if (data) {
          setMatchData(data);

          if (data.liveData && data.liveData.length > 0 && data.liveData[0].hls) {
            setVideoUrl(data.liveData[0].hls);
          } else if (data.links && data.links.length > 0) {
            setVideoUrl(data.links[0]);
          }

          if (data.liveData && data.liveData.length > 0 && data.liveData[0].blv) {
            try {
              const userInfo = await getUserInfo(data.liveData[0].blv);
              setBlvInfo(userInfo);
            } catch (error) {
            }
          }
        }
      } catch (error) {
        // Error fetching match details
      } finally {
        setLoading(false);
      }
    };

    if (matchId && matchId !== 'unknown') {
      getMatchDetails();
    }
  }, [matchId]);


  useEffect(() => {
    if (isScreenLocked) {
      const scrollY = window.scrollY;

      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      window.scrollTo(0, 0);

      document.body.classList.add('screen-locked');

      // Tính toán chiều cao video khi screen lock
      if (videoRef.current) {
        const videoElement = videoRef.current;
        const rect = videoElement.getBoundingClientRect();
        setVideoHeight(rect.height);
      }
    } else {
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';

      document.body.classList.remove('screen-locked');

      window.scrollTo(0, scrollY);
      setVideoHeight(0);
    }

    return () => {
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      document.body.classList.remove('screen-locked');
    };
  }, [isScreenLocked]);

  // Tính toán lại chiều cao video khi window resize
  useEffect(() => {
    const handleResize = () => {
      if (isScreenLocked && videoRef.current) {
        const videoElement = videoRef.current;
        const rect = videoElement.getBoundingClientRect();
        setVideoHeight(rect.height);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isScreenLocked]);

  // Tính toán chiều cao video sau khi video được render
  useEffect(() => {
    if (isScreenLocked && videoRef.current) {
      // Delay một chút để video được render hoàn toàn
      const timer = setTimeout(() => {
        if (videoRef.current) {
          const videoElement = videoRef.current;
          const rect = videoElement.getBoundingClientRect();
          setVideoHeight(rect.height);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isScreenLocked, videoUrl]);

  // Tính toán chiều cao viewport và vị trí video cho mobile
  useEffect(() => {
    const updateViewportAndVideoPosition = () => {
      // Lấy chiều cao thực tế của viewport (bao gồm cả thanh địa chỉ mobile)
      const vh = window.innerHeight;
      setMobileViewportHeight(vh);

      // Kiểm tra xem có phải mobile không
      setIsMobile(window.innerWidth < 1024);

      // Tính toán vị trí top của video container
      if (videoRef.current) {
        const videoRect = videoRef.current.getBoundingClientRect();
        const videoTop = videoRect.top + window.scrollY;
        setVideoTopPosition(videoTop);
      }
    };

    updateViewportAndVideoPosition();
    window.addEventListener('resize', updateViewportAndVideoPosition);
    window.addEventListener('orientationchange', updateViewportAndVideoPosition);
    window.addEventListener('scroll', updateViewportAndVideoPosition);

    return () => {
      window.removeEventListener('resize', updateViewportAndVideoPosition);
      window.removeEventListener('orientationchange', updateViewportAndVideoPosition);
      window.removeEventListener('scroll', updateViewportAndVideoPosition);
    };
  }, []);

  // Cập nhật vị trí video khi matchData thay đổi
  useEffect(() => {
    if (matchData && videoRef.current) {
      const timer = setTimeout(() => {
        const videoRect = videoRef.current?.getBoundingClientRect();
        if (videoRect) {
          const videoTop = videoRect.top + window.scrollY;
          setVideoTopPosition(videoTop);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [matchData]);

  // Cập nhật vị trí video khi component mount và sau khi render
  useEffect(() => {
    const updateVideoPosition = () => {
      if (videoRef.current) {
        const videoRect = videoRef.current.getBoundingClientRect();
        const videoTop = videoRect.top + window.scrollY;
        setVideoTopPosition(videoTop);
      }
    };

    // Cập nhật ngay lập tức
    updateVideoPosition();

    // Cập nhật sau khi render hoàn tất
    const timer = setTimeout(updateVideoPosition, 200);

    return () => clearTimeout(timer);
  }, [mobileViewportHeight]);

//   useEffect(() => {
//     const scrollToVideo = () => {
//       if (videoRef.current && isMobile) {
//         videoRef.current.scrollIntoView({
//           behavior: "smooth",
//           block: "start",
//         });
//       }
//     };
//     scrollToVideo();

//     const timer = setTimeout(() => {
//       scrollToVideo();
//     }, 1000);

//     return () => clearTimeout(timer);
//   }, [isMobile]);


  // Tự động khoá màn hình khi phát hiện mobile
  useEffect(() => {
    if (isMobile && isClient) {
      // Delay một chút để đảm bảo component đã render hoàn tất
      const autoLockTimer = setTimeout(() => {
        setIsScreenLocked(true);
      }, 1000); // Khoá sau 1 giây khi vào trang mobile

      return () => {
        clearTimeout(autoLockTimer);
      };
    }
  }, [isMobile, isClient, setIsScreenLocked]);

  // Tự động khoá màn hình ngay khi vào trang trên mobile
//   useEffect(() => {
//     if (isClient) {
//       // Kiểm tra ngay lập tức xem có phải mobile không
//       const checkMobileAndLock = () => {
//         const isMobileDevice = window.innerWidth < 1024;
//         if (isMobileDevice) {
//           // Khoá màn hình ngay lập tức trên mobile
//           setIsScreenLocked(true);
//         }
//       };

//       // Kiểm tra ngay lập tức
//       checkMobileAndLock();

//       // Kiểm tra lại sau khi render hoàn tất
//       const timer = setTimeout(checkMobileAndLock, 500);

//       return () => {
//         clearTimeout(timer);
//       };
//     }
//   }, [isClient, setIsScreenLocked]);

  // Handler functions
  const handleLoginSuccess = (userData: UserData) => {
    setIsLoggedIn(true);
  };

  const toggleLoginStatus = () => {
    setIsLoggedIn(!isLoggedIn);
  };

  const openAuthModal = (mode: 'login' | 'register' = 'login') => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const setAuthModalMode = (mode: 'login' | 'register') => {
    openAuthModal(mode);
  };

  const handleTabChange = (tabIndex: number, roomId?: string) => {
    setActiveTab(tabIndex);
    if (roomId) {
      setSelectedRoomId(roomId);
    }
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
    setAuthMode('login');
  };

  const toggleScreenLock = () => {
    setIsScreenLocked(!isScreenLocked);
  };

  const handleHomeClick = () => {
    window.location.href = '/';
  };

  const getIconColorClass = (isActive: boolean) => {
    if (isActive) {
      return "filter brightness-0 invert-[0.3] sepia-[1] saturate-[5] hue-rotate-[200deg]";
    }
    return "filter brightness-0 invert-[0.6] dark:invert-[0.4]";
  };

  return {
    // States
    activeTab,
    setActiveTab,
    isLoggedIn,
    setIsLoggedIn,
    isAuthModalOpen,
    setIsAuthModalOpen,
    authMode,
    setAuthMode,
    videoUrl,
    setVideoUrl,
    matchData,
    setMatchData,
    blvInfo,
    setBlvInfo,
    isClient,
    setIsClient,
    videoHeight,
    setVideoHeight,
    mobileViewportHeight,
    setMobileViewportHeight,
    videoTopPosition,
    setVideoTopPosition,
    isMobile,
    setIsMobile,
    loading,
    setLoading,
    selectedRoomId,
    setSelectedRoomId,
    isScreenLocked,
    setIsScreenLocked,
    chatContainerRef,
    videoRef,
    
    // Params
    matchId,
    streamerName,
    matchSlug,
    
    // Handlers
    handleScroll,
    handleLoginSuccess,
    toggleLoginStatus,
    openAuthModal,
    closeAuthModal,
    setAuthModalMode,
    handleTabChange,
    toggleScreenLock,
    handleHomeClick,
    getIconColorClass,
  };
}
