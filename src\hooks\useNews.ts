import { useState, useEffect, useCallback } from 'react';
import { newsService } from '@/services/news.service';
import type {
  PostWithSeo,
  PublishedPostWithSeo,
  PostsQueryParams,
  PostFilters
} from '@/types/news.types';

export interface UseNewsReturn {
  // State
  posts: PostWithSeo[];
  publishedPosts: PublishedPostWithSeo[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  
  // Actions
  fetchPosts: (params?: PostsQueryParams) => Promise<void>;
  fetchPublishedPosts: () => Promise<void>;
  refreshPosts: () => Promise<void>;
  getPostCount: (filters?: PostFilters) => Promise<void>;
}

export function useNews(initialParams?: PostsQueryParams): UseNewsReturn {
  const [posts, setPosts] = useState<PostWithSeo[]>([]);
  const [publishedPosts, setPublishedPosts] = useState<PublishedPostWithSeo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const fetchPosts = useCallback(async (params?: PostsQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await newsService.getPosts(params || initialParams);
      setPosts(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải tin tức');
      console.error('Error fetching posts:', err);
    } finally {
      setLoading(false);
    }
  }, [initialParams]);

  const fetchPublishedPosts = useCallback(async (params?: PostsQueryParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.getPublishedPosts(params);
      setPublishedPosts(result.posts);
      setTotalCount(result.total);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch published posts';
      setError(errorMessage);
      console.error('Error fetching published posts:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const getPostCount = useCallback(async (filters?: PostFilters) => {
    try {
      const count = await newsService.getPostCount(filters);
      setTotalCount(count);
    } catch (err) {
      console.error('Error getting post count:', err);
    }
  }, []);

  const refreshPosts = useCallback(async () => {
    await fetchPosts(initialParams);
  }, [fetchPosts, initialParams]);

  // Auto-fetch on mount
  useEffect(() => {
    fetchPublishedPosts();
  }, [fetchPublishedPosts]);

  return {
    posts,
    publishedPosts,
    loading,
    error,
    totalCount,
    fetchPosts,
    fetchPublishedPosts,
    refreshPosts,
    getPostCount
  };
}

export default useNews;