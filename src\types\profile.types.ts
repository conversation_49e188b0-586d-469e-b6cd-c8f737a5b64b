export interface Profile {
    id: string;
    email: string;
    full_name: string;
    role: string;
    avatar_url?: string;
    created_at?: string;
    updated_at?: string;
}

export interface UpdateProfileData {
    full_name?: string;
    avatar_url?: string;
    role?: string;
}

export interface ProfileResponse {
    profile: Profile | null;
    error: Error | null;
}

export interface ProfilesResponse {
    profiles: Profile[];
    error: Error | null;
}
