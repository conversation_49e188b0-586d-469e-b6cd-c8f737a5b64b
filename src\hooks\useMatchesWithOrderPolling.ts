import { useMemo, useState, useCallback, useEffect } from 'react';
import { MatchData } from '@/types/match';
import { type FilterType } from '@/constants/filters';
import { useMatches } from '@/hooks/useMatches';
import { useMatchOrderMapping } from '@/hooks/useMatchOrderMapping';
import { useLiveMatchesPolling } from '@/hooks/useLiveMatchesPolling';

// Extended MatchData with order information
export interface MatchDataWithOrder extends MatchData {
  hasOrder: boolean;
}

interface UseMatchesWithOrderPollingReturn {
  matches: MatchDataWithOrder[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  orderLoading: boolean;
  orderError: string | null;
  // Polling related
  isPollingLive: boolean;
  lastLiveUpdate: Date | null;
  liveMatchesCount: number;
  updatedMatchesCount: number;
  // Functions
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
  clearOrderError: () => void;
}

export const useMatchesWithOrderPolling = (
  enableLivePolling: boolean = true,
  pollingInterval: number = 3000
): UseMatchesWithOrderPollingReturn => {
  // Use the base matches hook
  const {
    matches: baseMatches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter: baseFetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatches();

  // Use the order mapping hook
  const {
    loading: orderLoading,
    error: orderError,
    hasOrder,
    getOrderValue,
    refreshOrderData,
    clearError: clearOrderError,
  } = useMatchOrderMapping();

  // State for polling updates
  const [pollingMatches, setPollingMatches] = useState<MatchData[]>([]);
  const [hasPollingUpdates, setHasPollingUpdates] = useState(false);

  // Get current category (assuming football as default)
  const currentCategory = 'football'; // You might want to get this from useMatches hook

  // Use live matches polling
  const {
    updateMatches,
    isPolling: isPollingLive,
    lastUpdateTime: lastLiveUpdate,
    updatedMatchesCount
  } = useLiveMatchesPolling(
    hasPollingUpdates ? pollingMatches : baseMatches,
    currentFilter,
    currentCategory,
    {
      pollingInterval,
      enablePolling: enableLivePolling
    }
  );

  // Enhanced fetchMatchesByFilter that resets polling state
  const fetchMatchesByFilter = useCallback(async (filter: FilterType, category?: string) => {
    setHasPollingUpdates(false);
    setPollingMatches([]);
    await baseFetchMatchesByFilter(filter, category);
  }, [baseFetchMatchesByFilter]);

  // Handle polling updates
  useEffect(() => {
    const handlePollingUpdate = (newMatches: MatchData[]) => {
      const updatedMatches = updateMatches(newMatches);
      setPollingMatches(updatedMatches);
      setHasPollingUpdates(true);
    };

    // Set up the callback for polling updates
    // This is a bit of a workaround since we can't directly pass callbacks to the polling hook
    // In a real implementation, you might want to use a more sophisticated state management solution
    
    return () => {
      // Cleanup if needed
    };
  }, [updateMatches]);

  // Determine which matches to use (polling updates or base matches)
  const currentMatches = hasPollingUpdates ? pollingMatches : baseMatches;

  // Combine matches with order information and sort by order priority
  const matches = useMemo((): MatchDataWithOrder[] => {
    const matchesWithOrder = currentMatches.map((match) => ({
      ...match,
      hasOrder: hasOrder(match.id),
    }));

    // Sort matches: by order value (ascending), then non-order matches
    return matchesWithOrder.sort((a, b) => {
      const aOrderValue = getOrderValue(a.id);
      const bOrderValue = getOrderValue(b.id);

      // If both have order values, sort by order number (ascending)
      if (aOrderValue !== null && bOrderValue !== null) {
        return aOrderValue - bOrderValue;
      }

      // If one has order and the other doesn't, prioritize the one with order
      if (aOrderValue !== null && bOrderValue === null) return -1;
      if (aOrderValue === null && bOrderValue !== null) return 1;

      // If both don't have order, maintain original order
      return 0;
    });
  }, [currentMatches, hasOrder, getOrderValue]);

  // Count live matches
  const liveMatchesCount = useMemo(() => {
    return matches.filter(match => match.status?.toLowerCase() === 'live').length;
  }, [matches]);

  // Log polling status changes
  useEffect(() => {
   
  }, [isPollingLive, liveMatchesCount]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    orderLoading,
    orderError,
    // Polling related
    isPollingLive,
    lastLiveUpdate,
    liveMatchesCount,
    updatedMatchesCount,
    // Functions
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    refreshOrderData,
    clearError,
    clearOrderError,
  };
};
