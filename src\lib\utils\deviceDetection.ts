/**
 * Device detection utilities
 */

export interface DeviceInfo {
  isMobile: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isTablet: boolean;
}

/**
 * Detect if the current device is iOS
 */
export const detectIOS = (): boolean => {
  if (typeof window === "undefined") return false;

  return (
    /iPad|iPhone|iPod/.test(navigator.userAgent) ||
    (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)
  );
};

/**
 * Detect if the current device is Android
 */
export const detectAndroid = (): boolean => {
  if (typeof window === "undefined") return false;

  return /Android/.test(navigator.userAgent);
};

/**
 * Detect if the current device is mobile
 */
export const detectMobile = (): boolean => {
  if (typeof window === "undefined") return false;

  // Kiểm tra user agent cho các thiết bị mobile phổ biến
  const mobileUserAgents =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  const isMobileUserAgent = mobileUserAgents.test(navigator.userAgent);

  // Kiểm tra screen width
  const isSmallScreen = window.innerWidth < 768;

  // Nếu là mobile device thực sự (theo user agent), luôn return true
  if (isMobileUserAgent) return true;

  // Nếu không phải mobile device, chỉ dựa vào screen width để responsive
  return isSmallScreen;
};

/**
 * Detect if the current device is tablet
 */
export const detectTablet = (): boolean => {
  if (typeof window === "undefined") return false;

  const width = window.innerWidth;
  return width >= 768 && width < 1024;
};

/**
 * Get comprehensive device information
 */
export const getDeviceInfo = (): DeviceInfo => {
  return {
    isMobile: detectMobile(),
    isIOS: detectIOS(),
    isAndroid: detectAndroid(),
    isTablet: detectTablet(),
  };
};

/**
 * Check if device supports visual viewport API
 */
export const supportsVisualViewport = (): boolean => {
  return typeof window !== "undefined" && "visualViewport" in window;
};
