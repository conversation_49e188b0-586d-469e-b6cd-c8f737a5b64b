"use client";

import { useState } from "react";
import MatchStatistics from "@/components/features/MatchStatistics";
import { MatchData } from "@/types/match";

// Mock match data for testing
const mockMatchData: MatchData = {
  id: "test-match-123",
  league: "Premier League",
  category: "football",
  homeTeam: {
    logo: "/logos/home-team.png",
    name: "Manchester United",
    score: 2,
  },
  awayTeam: {
    logo: "/logos/away-team.png", 
    name: "Liverpool",
    score: 1,
  },
  cards: {
    redAway: 0,
    redHome: 1,
    yellowAway: 2,
    yellowHome: 3,
  },
  odds: null,
  liveData: [],
  liveTrack: null,
  typeMatch: "Regular Season",
  status: "live", // This will trigger polling
  date: "2024-01-15",
  time: "15:30",
  links: [],
  viewFake: 1500,
  liveFake: 850,
  createdAt: "2024-01-15T10:00:00Z",
  updatedAt: "2024-01-15T15:45:00Z",
  parseData: {
    time: "78'",
    status: "2nd Half",
  },
  title: "Manchester United vs Liverpool",
  imageUrl: null,
  author: null,
  hashtags: null,
  timestamp: null,
  flv: null,
  _ownLeague: true,
  _ownHomeTeam: true,
  _ownAwayTeam: true,
  _ownCards: true,
  _ownOdds: false,
  _ownLiveTrack: false,
  _ownStatus: true,
  _ownDate: true,
  _ownTime: true,
  _ownParseData: true,
  key_sync: null,
  incidents: {
    cards: {
      red: 1,
      yellow: 5,
    },
    goals: {
      home: 2,
      away: 1,
    },
  },
  statistics: [
    {
      id: "stat-1",
      type: 0, // Full match
      stats: [
        {
          team_id: "home",
          goals: 2,
          shots: 12,
          shots_on_target: 6,
          shots_off_target: 6,
          ball_possession: 58,
          corner_kicks: 7,
          yellow_cards: 3,
          red_cards: 1,
          attacks: 45,
          dangerous_attack: 12,
          passes: 456,
          passes_accuracy: 85,
        },
        {
          team_id: "away",
          goals: 1,
          shots: 8,
          shots_on_target: 3,
          shots_off_target: 5,
          ball_possession: 42,
          corner_kicks: 4,
          yellow_cards: 2,
          red_cards: 0,
          attacks: 32,
          dangerous_attack: 8,
          passes: 398,
          passes_accuracy: 82,
        },
      ],
    },
  ],
};

export default function StatisticsDemo() {
  const [isLoading, setIsLoading] = useState(false);
  const [matchStatus, setMatchStatus] = useState<string>("live");

  const currentMatchData = {
    ...mockMatchData,
    status: matchStatus,
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Statistics Demo - Real-time Updates
          </h1>
          
          <div className="flex gap-4 mb-4">
            <button
              onClick={() => setMatchStatus("live")}
              className={`px-4 py-2 rounded-lg font-medium ${
                matchStatus === "live"
                  ? "bg-green-500 text-white"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
              }`}
            >
              Live (Polling ON)
            </button>
            
            <button
              onClick={() => setMatchStatus("finished")}
              className={`px-4 py-2 rounded-lg font-medium ${
                matchStatus === "finished"
                  ? "bg-red-500 text-white"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
              }`}
            >
              Finished (Polling OFF)
            </button>
            
            <button
              onClick={() => setIsLoading(!isLoading)}
              className={`px-4 py-2 rounded-lg font-medium ${
                isLoading
                  ? "bg-yellow-500 text-white"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
              }`}
            >
              {isLoading ? "Stop Loading" : "Start Loading"}
            </button>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            <p><strong>Current Status:</strong> {matchStatus}</p>
            <p><strong>Polling:</strong> {matchStatus === "live" && !isLoading ? "Active (every 3s)" : "Inactive"}</p>
            <p><strong>Loading:</strong> {isLoading ? "Yes" : "No"}</p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              How it works:
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• When status is "live", statistics are fetched every 3 seconds</li>
              <li>• Only re-renders when statistics data actually changes</li>
              <li>• Shows polling indicator when actively updating</li>
              <li>• Automatically stops polling when match is finished</li>
              <li>• Handles errors gracefully without breaking UI</li>
            </ul>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <MatchStatistics 
              matchData={currentMatchData} 
              isLoading={isLoading}
            />
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Technical Details
            </h3>
            
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Optimizations:
                </h4>
                <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Deep comparison prevents unnecessary re-renders</li>
                  <li>• useMemo for expensive calculations</li>
                  <li>• useCallback for stable function references</li>
                  <li>• Custom hook for reusable logic</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  Features:
                </h4>
                <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Automatic polling for live matches</li>
                  <li>• Error handling with user feedback</li>
                  <li>• Loading states and skeletons</li>
                  <li>• Configurable polling interval</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
