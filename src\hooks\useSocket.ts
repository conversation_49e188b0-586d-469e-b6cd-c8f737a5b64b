import { useEffect, useRef } from "react";
import { io, Socket } from "socket.io-client";

const SOCKET_URL =
  process.env.NEXT_PUBLIC_API_BACKEND || "https://backend.ngoaihangtv.xyz";

  
function now() {
  return new Date().toLocaleTimeString();
}
export function useSocket(matchId: string, onMatchUpdate: (data: any) => void) {
  const socketRef = useRef<Socket | null>(null);

  // Khởi tạo socket chỉ 1 lần
  useEffect(() => {
    const socket = io(SOCKET_URL, { transports: ["websocket"] });
    socketRef.current = socket;

    socket.on("connect", () => {
      console.log("✅ Connected:", socket.id);
    });

    socket.on("disconnect", () => {
      console.warn("⚠️ Socket disconnected");
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  // Lắng nghe matchId và callback
  useEffect(() => {
    const socket = socketRef.current;
    if (!socket) return;

    // join match mới
    socket.emit("join_match", matchId);

    // listener update
    const handler = (data: any) => {
      if (data.id === matchId) {
        onMatchUpdate(data);
      }
    };
    socket.on("match_update", handler);

    // interval emit 3s
    const interval = setInterval(() => {
      if (socket.connected) {
        console.log(`[${now()}] ⏱ Emit get_match_update:`, matchId);
        socket.emit("get_match_update", matchId);
      }
    }, 3000);

    // cleanup khi matchId hoặc callback đổi
    return () => {
      clearInterval(interval);
      socket.off("match_update", handler);
    };
  }, [matchId, onMatchUpdate]);

  return socketRef.current;
}
