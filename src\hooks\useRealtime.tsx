"use client";

import { useEffect, useState } from "react";
import { useSupabase } from "@/contexts/SupabaseContext";
import type { RealtimeChannel } from "@supabase/supabase-js";

interface UseRealtimeProps {
  table: string;
  filter?: string;
  onInsert?: (payload: { new: Record<string, unknown> }) => void;
  onUpdate?: (payload: { new: Record<string, unknown> }) => void;
  onDelete?: (payload: { old: Record<string, unknown> }) => void;
}

export function useRealtime({
  table,
  filter,
  onInsert,
  onUpdate,
  onDelete,
}: UseRealtimeProps) {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const { supabase } = useSupabase();

  useEffect(() => {
    const channelName = `realtime:${table}${filter ? `:${filter}` : ""}`;
    const realtimeChannel = supabase.channel(channelName);

    if (onInsert) {
      realtimeChannel.on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table,
          filter,
        },
        onInsert
      );
    }

    if (onUpdate) {
      realtimeChannel.on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table,
          filter,
        },
        onUpdate
      );
    }

    if (onDelete) {
      realtimeChannel.on(
        "postgres_changes",
        {
          event: "DELETE",
          schema: "public",
          table,
          filter,
        },
        onDelete
      );
    }

    realtimeChannel.subscribe();
    setChannel(realtimeChannel);

    return () => {
      if (realtimeChannel) {
        supabase.removeChannel(realtimeChannel);
      }
    };
  }, [table, filter, onInsert, onUpdate, onDelete, supabase]);

  return { channel };
}
