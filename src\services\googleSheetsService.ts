import { google } from 'googleapis';
import { readFileSync } from 'fs';
import { join } from 'path';

const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];

const getCredentials = () => {
  try {
    const credentialsPath = join(process.cwd(), 'databongda-392900d00c05.json');
    const credentialsContent = readFileSync(credentialsPath, 'utf8');
    return JSON.parse(credentialsContent);
  } catch (error) {
    throw new Error('Failed to read Google service account credentials');
  }
};

const getAuthClient = () => {
  const credentials = getCredentials();
  return new google.auth.GoogleAuth({
    credentials,
    scopes: SCOPES,
  });
};

const getSheetsClient = () => {
  const auth = getAuthClient();
  return google.sheets({ version: 'v4', auth });
};

export interface SheetData {
  range: string;
  majorDimension: string;
  values: Record<string, string>[];
}

export class GoogleSheetsService {
  /**
   * Format raw sheet data into key-value pairs
   * @param values - Raw sheet values array
   * @returns Formatted data array with key-value pairs
   */
  private static formatSheetData(values: string[][]): Record<string, string>[] {
    if (values.length < 2) return [];

    const headers = values[0];
    const dataRows = values.slice(1);

    return dataRows.map(row => {
      const formattedRow: Record<string, string> = {};
      headers.forEach((header, index) => {
        formattedRow[header] = row[index] || '';
      });
      return formattedRow;
    });
  }

  /**
   * Get data from Google Sheets
   * @param spreadsheetId - Spreadsheet ID
   * @param range - Range to fetch (e.g., 'lenkeo!A1:H')
   * @returns Promise<SheetData>
   */
  static async getSheetData(spreadsheetId: string, range: string): Promise<SheetData> {
    try {
      const sheets = getSheetsClient();
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: spreadsheetId,
        range: range,
      });

      const rawValues = response.data.values || [];
      const formattedValues = this.formatSheetData(rawValues);

      return {
        range: response.data.range || '',
        majorDimension: response.data.majorDimension || 'ROWS',
        values: formattedValues,
      };
    } catch (error) {
      // Error fetching sheet data
      throw new Error(`Failed to fetch sheet data: ${error}`);
    }
  }

  /**
   * Get data from 'lenkeo' sheet with range A1:H
   * @param spreadsheetId - Spreadsheet ID
   * @returns Promise<SheetData>
   */
  static async getLenKeoData(spreadsheetId: string): Promise<SheetData> {
    return this.getSheetData(spreadsheetId, 'lenkeo!A1:H');
  }

  /**
   * Get data from specific sheet with custom range
   * @param spreadsheetId - Spreadsheet ID
   * @param sheetName - Sheet name
   * @param startCell - Start cell (e.g., 'A1')
   * @param endCell - End cell (e.g., 'H')
   * @returns Promise<SheetData>
   */
  static async getSheetDataByRange(
    spreadsheetId: string,
    sheetName: string,
    startCell: string = 'A1',
    endCell: string = 'H'
  ): Promise<SheetData> {
    const range = `${sheetName}!${startCell}:${endCell}`;
    return this.getSheetData(spreadsheetId, range);
  }

  /**
   * Get data from multiple sheets at once
   * @param spreadsheetId - Spreadsheet ID
   * @param ranges - Array of ranges to fetch
   * @returns Promise<SheetData[]>
   */
  static async getMultipleRanges(spreadsheetId: string, ranges: string[]): Promise<SheetData[]> {
    try {
      const sheets = getSheetsClient();
      const response = await sheets.spreadsheets.values.batchGet({
        spreadsheetId: spreadsheetId,
        ranges: ranges,
      });

      return (response.data.valueRanges || []).map((range, index) => {
        const rawValues = range.values || [];
        const formattedValues = this.formatSheetData(rawValues);

        return {
          range: range.range || ranges[index] || '',
          majorDimension: range.majorDimension || 'ROWS',
          values: formattedValues,
        };
      });
    } catch (error) {
      throw new Error(`Failed to fetch multiple ranges: ${error}`);
    }
  }

  /**
   * Get spreadsheet metadata information
   * @param spreadsheetId - Spreadsheet ID
   * @returns Promise<unknown>
   */
  static async getSpreadsheetInfo(spreadsheetId: string): Promise<unknown> {
    try {
      const sheets = getSheetsClient();
      const response = await sheets.spreadsheets.get({
        spreadsheetId: spreadsheetId,
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch spreadsheet info: ${error}`);
    }
  }

  /**
 * Updates a specific cell in a Google Sheet.
 * @param spreadsheetId - The ID of the Google Spreadsheet.
 * @param sheetName - The name of the sheet to update.
 * @param order - The order value to locate the row.
 * @param column - The column name to update (e.g., 'displayed', 'winDisplayed').
 * @param value - The value to set in the cell.
 * @returns Promise<void> - A promise that resolves when the cell is updated.
 * @throws Error if the sheet is empty, the column is not found, or the row is not found.
 */
  static async updateSheetCell(
    spreadsheetId: string,
    sheetName: string,
    order: string | number,
    column: string,
    value: string | number | boolean
  ): Promise<void> {
    try {
      const sheets = getSheetsClient();

      // Fetch data to find the column and row indices
      const range = `${sheetName}!A1:Z`;
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
      });

      const values = response.data.values || [];
      if (values.length < 1) {
        throw new Error('Sheet is empty or has no headers');
      }

      // Find the index of the column header
      const headers = values[0];
      const columnIndex = headers.indexOf(column);
      if (columnIndex === -1) {
        throw new Error(`Column ${column} not found in sheet`);
      }

      // Find the row with the matching order value
      let rowIndex = -1;
      for (let i = 1; i < values.length; i++) {
        if (values[i][0] == order) { // Assumes 'order' is in column A
          rowIndex = i + 1; // +1 because Google Sheets API uses 1-based indexing
          break;
        }
      }

      if (rowIndex === -1) {
        throw new Error(`Row with order ${order} not found`);
      }

      // Update the cell value
      const updateRange = `${sheetName}!${String.fromCharCode(65 + columnIndex)}${rowIndex}`;
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: updateRange,
        valueInputOption: 'RAW',
        requestBody: {
          values: [[value]],
        },
      });

    } catch (error) {
      // Error updating sheet cell
      throw new Error(`Failed to update sheet cell: ${error}`);
    }
  }
}


// Export default instance
export default GoogleSheetsService;
