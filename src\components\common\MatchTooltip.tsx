"use client";

import { StatisticsData, StatisticsResponse } from "@/types/match";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
type MatchStats = StatisticsData;

interface MatchStatsPopupProps {
  matchId: string;
  homeTeamName: string;
  awayTeamName: string;
  leagueName: string;
  matchStatus: string;
  statistics?: StatisticsResponse[]; // Statistics data từ match
  // Legacy props for backward compatibility
  matchStats?: StatisticsData[];
  homeTeamId?: string;
  awayTeamId?: string;
}

export default function MatchTooltip({
  matchId,
  homeTeamName,
  awayTeamName,
  leagueName,
  matchStatus,
  statistics,
  matchStats: legacyMatchStats,
}: MatchStatsPopupProps) {
  const [activeTab, setActiveTab] = useState("first");
  const [matchStats, setMatchStats] = useState<MatchStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch match statistics
  useEffect(() => {
    const processStats = () => {
      setLoading(true);
      setError(null);

      try {
        if (statistics && Array.isArray(statistics) && statistics.length > 0) {

          const orderedStats: (MatchStats | null)[] = [null, null, null];
          statistics.forEach((statGroup: StatisticsResponse) => {
            if (statGroup.stats && statGroup.stats.length >= 2) {
              const homeStats = statGroup.stats[0];
              const awayStats = statGroup.stats[1];

              const matchStat: MatchStats = {
                possession: {
                  home: homeStats.ball_possession || 0,
                  away: awayStats.ball_possession || 0,
                },
                corners: {
                  home: homeStats.corner_kicks || 0,
                  away: awayStats.corner_kicks || 0,
                },
                goals: {
                  home: homeStats.goals || 0,
                  away: awayStats.goals || 0,
                },
                cards: {
                  home:
                    (homeStats.yellow_cards || 0) + (homeStats.red_cards || 0),
                  away:
                    (awayStats.yellow_cards || 0) + (awayStats.red_cards || 0),
                },
                shots: {
                  home: homeStats.shots || 0,
                  away: awayStats.shots || 0,
                },
                attacks: {
                  home: homeStats.attacks || 0,
                  away: awayStats.attacks || 0,
                },
                passes: {
                  home: homeStats.passes || 0,
                  away: awayStats.passes || 0,
                },
              };

              if (statGroup.type === 0) {
                orderedStats[0] = matchStat; // Cả trận
              } else if (statGroup.type === 1) {
                orderedStats[1] = matchStat; // Hiệp 1
              } else if (statGroup.type === 2) {
                orderedStats[2] = matchStat; // Hiệp 2
              }
            }
          });

          const finalStats: MatchStats[] = [];

          finalStats[0] = orderedStats[0] ||
            orderedStats[1] ||
            orderedStats[2] || {
              possession: { home: 50, away: 50 },
              corners: { home: 0, away: 0 },
              goals: { home: 0, away: 0 },
              cards: { home: 0, away: 0 },
              shots: { home: 0, away: 0 },
              attacks: { home: 0, away: 0 },
              passes: { home: 0, away: 0 },
            };

          finalStats[1] = orderedStats[1] || finalStats[0];
          finalStats[2] = orderedStats[2] || finalStats[0];

          setMatchStats(finalStats);
        }
      } catch (err) {
        // Failed to process statistics
        setError("Lỗi khi xử lý dữ liệu thống kê");
      } finally {
        setLoading(false);
      }
    };

    processStats();
  }, [statistics, legacyMatchStats]);

  if (loading) {
    return (
      <div className="flex items-center justify-center">
        <div
          className="rounded-lg p-4 w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="text-white text-center">
            <h3 className="font-medium mb-2">{leagueName}</h3>
            <p className="text-sm font-semibold text-gray-200 mb-4">
              {homeTeamName} vs {awayTeamName}
            </p>
            <div className="flex items-center justify-center">
              <div className="animate-spin h-6 w-6 border-2 border-white border-t-transparent rounded-full"></div>
              <span className="ml-2 text-sm">Đang tải thống kê...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center">
        <div
          className="rounded-lg p-4 w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="text-white text-center">
            <h3 className="font-medium mb-2">{leagueName}</h3>
            <p className="text-sm font-semibold text-gray-200 mb-4">
              {homeTeamName} vs {awayTeamName}
            </p>
            <p className="text-xs text-gray-400">
              {error}
            </p>
            <p className="text-xs text-gray-400 mt-2">Status: {matchStatus}</p>
          </div>
        </div>
      </div>
    );
  }

  // Use default stats if no data available
  const displayStats = matchStats && matchStats.length > 0 ? matchStats : [
    {
      possession: { home: 50, away: 50 },
      corners: { home: 0, away: 0 },
      goals: { home: 0, away: 0 },
      cards: { home: 0, away: 0 },
      shots: { home: 0, away: 0 },
      attacks: { home: 0, away: 0 },
      passes: { home: 0, away: 0 },
    }
  ];

  // Map tab to matchStats index
  const getStatsIndex = (tab: string) => {
    switch (tab) {
      case "first":
        return 1;
      case "second":
        return 2;
      case "full":
        return 0;
      default:
        return 0;
    }
  };

  const statsIndex = getStatsIndex(activeTab);
  const stats = displayStats[statsIndex] || displayStats[0];

  if (!stats) {
    return (
      <div className="flex items-center justify-center">
        <div
          className="rounded-lg p-4 w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="text-white text-center">
            <h3 className="font-medium mb-2">{leagueName}</h3>
            <p className="text-sm font-semibold text-gray-200 mb-4">
              {homeTeamName} vs {awayTeamName}
            </p>
            <p className="text-xs text-gray-400">
              Không có dữ liệu thống kê chi tiết
            </p>
            <p className="text-xs text-gray-400 mt-2">Status: {matchStatus}</p>
          </div>
        </div>
      </div>
    );
  }

  const getStatValue = (value: number, isPercentage = false) =>
    isPercentage ? `${value}%` : value.toString();

  const getStatBarWidth = (
    homeValue: number,
    awayValue: number,
    isPercentage = false
  ) => {
    const total = homeValue + awayValue;
    return total > 0 ? (homeValue / total) * 100 : 50;
  };

  const statsData = [
    {
      label: "Tỷ lệ kiểm soát bóng",
      homeValue: stats.possession?.home || 0,
      awayValue: stats.possession?.away || 0,
      isPercentage: true,
    },
    {
      label: "Phạt góc",
      homeValue: stats.corners?.home || 0,
      awayValue: stats.corners?.away || 0,
    },
    {
      label: "Bàn thắng",
      homeValue: stats.goals?.home || 0,
      awayValue: stats.goals?.away || 0,
    },
    {
      label: "Thẻ",
      homeValue: stats.cards?.home || 0,
      awayValue: stats.cards?.away || 0,
    },
    {
      label: "Sút bóng",
      homeValue: stats.shots?.home || 0,
      awayValue: stats.shots?.away || 0,
    },
    {
      label: "Tấn công",
      homeValue: stats.attacks?.home || 0,
      awayValue: stats.attacks?.away || 0,
    },
    {
      label: "Chuyền bóng",
      homeValue: stats.passes?.home || 0,
      awayValue: stats.passes?.away || 0,
    },
  ];

  const RenderStats = () => (
    <div className="z-50 flex items-center justify-center w-full">
      <div
        className="py-1 w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-medium text-sm">{leagueName}</h3>
          <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
            {matchStatus === "live" ? "LIVE" : matchStatus.toUpperCase()}
          </div>
        </div>

        {/* Match Info */}
        <div className="text-center mb-4">
          <p className="text-sm font-semibold text-gray-200">
            {homeTeamName} vs {awayTeamName}
          </p>
        </div>

        {/* Statistics */}
        <div className="space-y-3 max-h-64 overflow-y-auto scrollbar-hide">
          {statsData.map((stat, index) => (
            <div key={index} className="space-y-2">
              {/* Values Row */}
              <div className="flex items-center justify-between text-xs">
                <span className="text-white font-medium w-16 text-right">
                  {getStatValue(stat.homeValue, stat.isPercentage)}
                </span>
                <span className="text-gray-300 text-center flex-1 px-2">
                  {stat.label}
                </span>
                <span className="text-white font-medium w-16 text-left">
                  {getStatValue(stat.awayValue, stat.isPercentage)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${getStatBarWidth(
                      stat.homeValue,
                      stat.awayValue
                    )}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Config tab
  const tabConfig = [
    { value: "first", label: "Hiệp 1" },
    { value: "second", label: "Hiệp 2" },
    { value: "full", label: "Cả trận" },
  ];

  return (
    <Tabs
      defaultValue="first"
      className="w-full"
      onValueChange={(val) => setActiveTab(val)}
      onClick={(e) => e.stopPropagation()}
    >
      <TabsList className="flex justify-evenly w-full">
        {tabConfig.map((tab) => (
          <TabsTrigger 
            key={tab.value} 
            value={tab.value}
            onClick={(e) => e.stopPropagation()}
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabConfig.map((tab) => (
        <TabsContent 
          key={tab.value} 
          value={tab.value}
          onClick={(e) => e.stopPropagation()}
        >
          <RenderStats />
        </TabsContent>
      ))}
    </Tabs>
  );
}