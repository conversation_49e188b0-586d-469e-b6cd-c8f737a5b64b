// ========================================
// NEWS SERVICE - Database Operations
// ========================================

import { createClient } from '@/lib/supabase/client';
import type { SupabaseClient } from '@supabase/supabase-js';
import type {
  CreatePostRequest,
  PostFilters,
  PostsQueryParams,
  PostWithSeo,
  PublishedPostWithSeo,
  UpdatePostRequest
} from "@/types/news.types";

class NewsService {
  private supabase;

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new NewsService(createClient());
  }

  // ========================================
  // POST OPERATIONS
  // ========================================

  /**
   * Create a new post
   */
  async createPost(postData: CreatePostRequest): Promise<PostWithSeo | null> {
    try {
      const { data, error } = await this.supabase
        .from('post')
        .insert([postData])
        .select(`
          *,
          author:profiles(
            id,
            full_name,
            email,
            role
          )
        `)
        .single();

      if (error) {
        console.error('Error creating post:', error);
        return null;
      }

      return data as PostWithSeo;
    } catch (error) {
      console.error('Error in createPost:', error);
      return null;
    }
  }

  /**
   * Update an existing post
   */
  async updatePost(id: string, postData: UpdatePostRequest): Promise<PostWithSeo | null> {
    try {
      const { data, error } = await this.supabase
        .from('post')
        .update({ ...postData, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select(`
          *,
          author:profiles(
            id,
            full_name,
            email,
            role
          )
        `)
        .single();

      if (error) {
        console.error('Error updating post:', error);
        return null;
      }

      return data as PostWithSeo;
    } catch (error) {
      console.error('Error in updatePost:', error);
      return null;
    }
  }

  /**
   * Delete a post
   */
  async deletePost(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('post')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting post:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deletePost:', error);
      return false;
    }
  }

  /**
   * Publish a post
   */
  async publishPost(id: string): Promise<PostWithSeo | null> {
    return this.updatePost(id, { published_at: new Date().toISOString() });
  }

  /**
   * Unpublish a post
   */
  async unpublishPost(id: string): Promise<PostWithSeo | null> {
    return this.updatePost(id, { published_at: null });
  }

  /**
   * Get all posts with filters and pagination
   */
  async getPosts(params: PostsQueryParams = {}): Promise<PostWithSeo[]> {
    try {
      let query = this.supabase
        .from('post')
        .select(`
          *,
          author:profiles(
            id,
            full_name,
            email,
            role
          )
        `);

      // Apply filters
      if (params.author_id) {
        query = query.eq('author_id', params.author_id);
      }

      if (params.published !== undefined) {
        if (params.published) {
          query = query.not('published_at', 'is', null);
        } else {
          query = query.is('published_at', null);
        }
      }

      if (params.search) {
        query = query.or(`title.ilike.%${params.search}%,content.ilike.%${params.search}%`);
      }

      // Apply sorting
      const sortBy = params.sort_by || 'created_at';
      const sortOrder = params.sort_order || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (params.limit) {
        query = query.limit(params.limit);
      }
      if (params.offset) {
        query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching posts:', error);
        return [];
      }

      return data as PostWithSeo[];
    } catch (error) {
      console.error('Error in getPosts:', error);
      return [];
    }
  }

  /**
   * Get a single post by ID
   */
  async getPost(id: string): Promise<PostWithSeo | null> {
    try {
      const { data, error } = await this.supabase
        .from('post')
        .select(`
          *,
          author:profiles(
            id,
            full_name,
            email,
            role
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching post:', error);
        return null;
      }

      return data as PostWithSeo;
    } catch (error) {
      console.error('Error in getPost:', error);
      return null;
    }
  }

  /**
   * Get a post by slug (now from post table)
   */
  async getPostBySlug(slug: string): Promise<PostWithSeo | null> {
    try {
      const { data, error } = await this.supabase
        .from('post')
        .select(`
          *,
          author:profiles(
            id,
            full_name,
            email,
            role
          )
        `)
        .eq('slug', slug)
        .single();

      if (error) {
        console.error('Error fetching post by slug:', error);
        return null;
      }

      return data as PostWithSeo;
    } catch (error) {
      console.error('Error in getPostBySlug:', error);
      return null;
    }
  }

  /**
   * Get published posts using the database function
   */
  async getPublishedPosts(params?: PostsQueryParams): Promise<{ posts: PublishedPostWithSeo[]; total: number }> {
    try {
      // Use the SQL function for better performance
      const { data, error } = await this.supabase
        .rpc('get_published_posts_with_seo');

      if (error) {
        console.error('Error fetching published posts:', error);
        throw new Error(`Failed to fetch published posts: ${error.message}`);
      }

      interface DatabasePost {
        post_id: string;
        title: string;
        content: string;
        description: string | null;
        thumbnail: string | null;
        published_at: string;
        slug: string | null;
        meta_title: string | null;
        meta_description: string | null;
        meta_keywords: string | null;
        author_name: string | null;
      }

      let posts: PublishedPostWithSeo[] = (data || []).map((post: DatabasePost) => ({
        id: post.post_id,
        title: post.title,
        content: post.content,
        description: post.description,
        thumbnail: post.thumbnail,
        published_at: post.published_at,
        slug: post.slug,
        meta_title: post.meta_title,
        meta_description: post.meta_description,
        meta_keywords: post.meta_keywords,
        author_name: post.author_name || 'Unknown Author'
      }));

      // Apply client-side filters if needed
      if (params?.search) {
        const searchLower = params.search.toLowerCase();
        posts = posts.filter(post => 
          post.title.toLowerCase().includes(searchLower) ||
          post.content.toLowerCase().includes(searchLower)
        );
      }

      if (params?.author_id) {
        // Note: We would need to modify the SQL function to include author_id for this filter
        // For now, this filter is not applied when using the SQL function
      }

      // Apply sorting
       if (params?.sort_by && params?.sort_order) {
         posts.sort((a, b) => {
           const aValue = a[params.sort_by as keyof PublishedPostWithSeo];
           const bValue = b[params.sort_by as keyof PublishedPostWithSeo];
           
           // Handle null/undefined values
           if (aValue == null && bValue == null) return 0;
           if (aValue == null) return params.sort_order === 'asc' ? -1 : 1;
           if (bValue == null) return params.sort_order === 'asc' ? 1 : -1;
           
           if (params.sort_order === 'asc') {
             return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
           } else {
             return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
           }
         });
       }

      const total = posts.length;

      // Apply pagination
      if (params?.page && params?.limit) {
        const from = (params.page - 1) * params.limit;
        const to = from + params.limit;
        posts = posts.slice(from, to);
      } else if (params?.offset !== undefined && params?.limit) {
        posts = posts.slice(params.offset, params.offset + params.limit);
      }

      return {
        posts,
        total
      };
    } catch (error) {
      console.error('Error in getPublishedPosts:', error);
      throw error;
    }
  }

  // ========================================
  // SEO OPERATIONS (now part of Post)
  // ========================================
  // SEO methods removed - SEO fields are now part of Post table

  // ========================================
  // UTILITY FUNCTIONS
  // ========================================

  /**
   * Generate a URL-friendly slug from title
   */
  generateSlug(title: string): string {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Check if a slug is available
   */
  async validateSlug(slug: string, excludeId?: string): Promise<boolean> {
    try {
      let query = this.supabase
        .from('post')
        .select('id')
        .eq('slug', slug);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error validating slug:', error);
        return false;
      }

      return data.length === 0;
    } catch (error) {
      console.error('Error in validateSlug:', error);
      return false;
    }
  }

  /**
   * Generate a unique slug from title
   */
  async generateUniqueSlug(title: string, excludeId?: string): Promise<string> {
    const baseSlug = this.generateSlug(title);
    let slug = baseSlug;
    let counter = 1;

    while (!(await this.validateSlug(slug, excludeId))) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Get post count by filters
   */
  async getPostCount(filters: PostFilters = {}): Promise<number> {
    try {
      let query = this.supabase
        .from('post')
        .select('id', { count: 'exact', head: true });

      // Apply filters
      if (filters.author_id) {
        query = query.eq('author_id', filters.author_id);
      }

      if (filters.published !== undefined) {
        if (filters.published) {
          query = query.not('published_at', 'is', null);
        } else {
          query = query.is('published_at', null);
        }
      }

      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);
      }

      const { count, error } = await query;

      if (error) {
        console.error('Error getting post count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getPostCount:', error);
      return 0;
    }
  }
}

// Export default instance
export const newsService = NewsService.createWithDefaultClient();
export default newsService;
export { NewsService };
