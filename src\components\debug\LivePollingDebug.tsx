"use client";

import { useState, useEffect } from "react";
import { useMatchesWithOrder } from "@/hooks/useMatchesWithOrder";

export default function LivePollingDebug() {
  const [renderCount, setRenderCount] = useState(0);
  const [lastRenderTime, setLastRenderTime] = useState<Date>(new Date());
  
  const {
    matches,
    loading,
    isPollingLive,
    liveMatchesCount,
    lastLiveUpdate,
    updatedMatchesCount,
    fetchMatchesByFilter,
  } = useMatchesWithOrder();

  // Count renders and log
  useEffect(() => {
    const newRenderCount = renderCount + 1;
    const now = new Date();
    setRenderCount(newRenderCount);
    setLastRenderTime(now);
    
    console.log(`🔄 LivePollingDebug render #${newRenderCount} at ${now.toLocaleTimeString()}`);
    console.log(`📊 Current state:`, {
      matchesCount: matches.length,
      liveMatchesCount,
      isPollingLive,
      lastLiveUpdate: lastLiveUpdate?.toLocaleTimeString(),
      updatedMatchesCount
    });
  });

  // Load live matches on mount
  useEffect(() => {
    console.log('🚀 Loading LIVE matches for debug...');
    fetchMatchesByFilter('live', 'football');
  }, [fetchMatchesByFilter]);

  const liveMatches = matches.filter(match => match.status?.toLowerCase() === 'live');

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            🐛 Live Polling Debug
          </h1>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
              <div className="text-xs text-blue-600 dark:text-blue-400">Renders</div>
              <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                {renderCount}
              </div>
              <div className="text-xs text-blue-500 dark:text-blue-400">
                {lastRenderTime.toLocaleTimeString()}
              </div>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded">
              <div className="text-xs text-green-600 dark:text-green-400">Polling</div>
              <div className={`text-lg font-bold ${isPollingLive ? 'text-green-700' : 'text-gray-500'}`}>
                {isPollingLive ? (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    ON
                  </div>
                ) : 'OFF'}
              </div>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded">
              <div className="text-xs text-purple-600 dark:text-purple-400">Live Count</div>
              <div className="text-lg font-bold text-purple-700 dark:text-purple-300">
                {liveMatchesCount}
              </div>
            </div>
            
            <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded">
              <div className="text-xs text-orange-600 dark:text-orange-400">Updates</div>
              <div className="text-lg font-bold text-orange-700 dark:text-orange-300">
                {updatedMatchesCount}
              </div>
            </div>
            
            <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded">
              <div className="text-xs text-red-600 dark:text-red-400">Last Update</div>
              <div className="text-xs font-bold text-red-700 dark:text-red-300">
                {lastLiveUpdate ? lastLiveUpdate.toLocaleTimeString() : 'Never'}
              </div>
            </div>
          </div>
        </div>

        {/* Console Instructions */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            🔍 Debug Instructions:
          </h3>
          <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
            <p><strong>1. Open Developer Console (F12)</strong></p>
            <p><strong>2. Watch for these logs every 3 seconds:</strong></p>
            <ul className="ml-4 space-y-1">
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">🔄 Polling X live matches...</code></li>
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">📊 API returned X matches</code></li>
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">🔍 Checking match ID: Team A X-Y Team B</code></li>
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">🔄 Match ID changed: [details]</code></li>
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">✅ Found X updated live matches</code></li>
            </ul>
            <p><strong>3. If no changes detected, you'll see:</strong></p>
            <ul className="ml-4">
              <li>• <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">⏭️ No changes in live matches - skipping update</code></li>
            </ul>
          </div>
        </div>

        {/* Live Matches Detail */}
        {liveMatches.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              Live Matches Being Monitored ({liveMatches.length})
            </h2>
            
            <div className="space-y-4">
              {liveMatches.map((match) => (
                <div
                  key={match.id}
                  className="border border-red-200 dark:border-red-800 rounded-lg p-4 bg-red-50 dark:bg-red-900/20"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-red-600 dark:text-red-400 uppercase">
                        {match.status}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {match.parseData?.time || match.time}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                      ID: {match.id}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-6 mb-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {match.homeTeam.name}
                      </span>
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">
                        {match.homeTeam.score}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {match.awayTeam.name}
                      </span>
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">
                        {match.awayTeam.score}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-3 border-t border-red-200 dark:border-red-700 text-xs text-gray-600 dark:text-gray-400">
                    <div>
                      <span className="font-medium">League:</span> {match.league}
                    </div>
                    <div>
                      <span className="font-medium">Updated:</span> {match.updatedAt ? new Date(match.updatedAt).toLocaleTimeString() : 'N/A'}
                    </div>
                    <div>
                      <span className="font-medium">Cards:</span> 
                      <span className="ml-1">🟨{match.cards?.yellowHome || 0}+{match.cards?.yellowAway || 0}</span>
                      <span className="ml-1">🟥{match.cards?.redHome || 0}+{match.cards?.redAway || 0}</span>
                    </div>
                    <div>
                      <span className="font-medium">Parse Time:</span> {match.parseData?.time || 'N/A'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Live Matches */}
        {!loading && liveMatches.length === 0 && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Live Matches Found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Polling is inactive. Try switching to a different filter or wait for live matches to start.
            </p>
            <button
              onClick={() => fetchMatchesByFilter('live', 'football')}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              🔄 Refresh Live Matches
            </button>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-500 dark:text-gray-400">Loading matches...</p>
          </div>
        )}

      </div>
    </div>
  );
}
