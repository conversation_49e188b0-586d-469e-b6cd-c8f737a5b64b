"use client";

import { TAB_INDICES, TabIndex } from "@/constants/tabs";
import { useAuth } from "@/contexts/AuthContext";
import { MatchData } from "@/types/match";
import { useState } from "react";
import LineupTab from "../LineupTab";
import StatsTab from "../StatsTab";
import DirectChatRoom from "./DirectChatRoom";
import GeneralChatRoom from "./GeneralChatRoom";
import RelatedMatchesTab from "../RelatedMatchesTab";

interface ChatTabProps {
  isLoggedIn: boolean;
  matchId?: string;
  chatType?: "general" | "direct";
  selectedRoomId?: string | null;
  activeTab: number;
  matchData?: MatchData | null;
  systemMessage?: {
    id: string;
    content: string;
    created_at: string;
    user_id: string;
    user: {
      full_name: string;
      avatar_url: string;
    };
  } | null;
  onOpenAuthModal: (mode: "login" | "register") => void;
  onTabChange: (tabIndex: TabIndex) => void;
}

export default function ChatTab({
  isLoggedIn,
  matchData,
  activeTab,
  matchId,
  onOpenAuthModal,
  onTabChange,
}: ChatTabProps) {
  const { user, chatRooms, loadMessagesForRoom, sendMessage, createChat } =
    useAuth();

  const [selectedDirectRoomId, setSelectedDirectRoomId] = useState<
    string | null
  >(null);

  // Handle room change for direct chat
  const handleRoomChange = (roomId: string) => {
    setSelectedDirectRoomId(roomId);
  };

  const handleTabChange = (tabIndex: TabIndex, roomId?: string) => {
    onTabChange(tabIndex);
    if (tabIndex === TabIndex.PRIVATE_CHAT) {
      setSelectedDirectRoomId(roomId || null);
    }
  };

  if (activeTab === TabIndex.PRIVATE_CHAT) {
    const directRooms =
      chatRooms?.filter((room) => room.type === "direct") || [];
    return (
      <DirectChatRoom
        user={user}
        selectedDirectRoomId={selectedDirectRoomId}
        sendMessage={sendMessage}
        loadMessagesForRoom={loadMessagesForRoom}
        directChatRooms={directRooms}
        isLoggedIn={isLoggedIn}
        onOpenAuthModal={onOpenAuthModal}
        onRoomChange={handleRoomChange}
        onCreateChat={createChat}
      />
    );
  }

  if (activeTab === TAB_INDICES.STATS) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 min-h-0 overflow-y-auto">
          <StatsTab isLoading={!matchData} matchData={matchData} />
        </div>
      </div>
    );
  }

  if (activeTab === TAB_INDICES.LINEUP) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 min-h-0 overflow-y-auto">
          <LineupTab isLoading={!matchData} />
        </div>
      </div>
    );
  }

  if (activeTab === TAB_INDICES.RELATED_MATCHES) {
    return (
      <div className="h-full flex">
        <div className="flex-1 min-h-0 overflow-y-auto">
          <RelatedMatchesTab
            currentMatchId={matchId}
            currentCategory={matchData?.category || "football"}
          />
        </div>
      </div>
    );
  }

  const roomId =
    chatRooms.find((room) => room.type === "general")?.id ??
    "00000000-0000-0000-0000-000000000001";

  return (
    <GeneralChatRoom
      roomId={roomId}
      isLoggedIn={isLoggedIn}
      onOpenAuthModal={onOpenAuthModal}
      onCreateChat={createChat}
      onTabChange={handleTabChange}
    />
  );
}
