import { useMemo, useEffect, useRef, useCallback, useState } from 'react';
import { MatchData } from '@/types/match';
import { type FilterType, FILTER_TYPES } from '@/constants/filters';
import { useMatches } from '@/hooks/useMatches';
import { useMatchOrderMapping } from '@/hooks/useMatchOrderMapping';
import { fetchMatches } from '@/services/matchService';

// Extended MatchData with order information
export interface MatchDataWithOrder extends MatchData {
  hasOrder: boolean;
}

interface UseMatchesWithOrderReturn {
  matches: MatchDataWithOrder[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  orderLoading: boolean;
  orderError: string | null;
  // Live polling related
  isPollingLive: boolean;
  liveMatchesCount: number;
  lastLiveUpdate: Date | null;
  updatedMatchesCount: number;
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
  clearOrderError: () => void;
}

// Deep comparison for essential match fields
const deepEqual = (obj1: MatchData, obj2: MatchData): boolean => {
  const essentialFields: (keyof MatchData)[] = [
    'status', 'homeTeam', 'awayTeam', 'cards', 'parseData', 'updatedAt'
  ];

  for (const field of essentialFields) {
    const val1 = obj1[field];
    const val2 = obj2[field];

    if (typeof val1 === 'object' && typeof val2 === 'object') {
      if (JSON.stringify(val1) !== JSON.stringify(val2)) {
        return false;
      }
    } else if (val1 !== val2) {
      return false;
    }
  }
  return true;
};

// Custom hook for interval
const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;
    const id = setInterval(() => savedCallback.current(), delay);
    return () => clearInterval(id);
  }, [delay]);
};

export const useMatchesWithOrder = (): UseMatchesWithOrderReturn => {
  // Use the base matches hook
  const {
    matches: baseMatches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter: baseFetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatches();

  // Live polling state
  const [pollingMatches, setPollingMatches] = useState<MatchData[]>([]);
  const [isPollingLive, setIsPollingLive] = useState(false);
  const [lastLiveUpdate, setLastLiveUpdate] = useState<Date | null>(null);
  const [updatedMatchesCount, setUpdatedMatchesCount] = useState(0);
  const [hasPollingUpdates, setHasPollingUpdates] = useState(false);
  const currentMatchesRef = useRef<MatchData[]>([]);

  // Use the order mapping hook
  const {
    loading: orderLoading,
    error: orderError,
    hasOrder,
    getOrderValue,
    refreshOrderData,
    clearError: clearOrderError,
  } = useMatchOrderMapping();

  // Get current matches (polling updates or base matches)
  const currentMatches = hasPollingUpdates ? pollingMatches : baseMatches;

  // Update ref with current matches
  useEffect(() => {
    currentMatchesRef.current = currentMatches;
  }, [currentMatches]);

  // Get live matches
  const liveMatches = useMemo(() => {
    return currentMatches.filter(match => match.status?.toLowerCase() === 'live');
  }, [currentMatches]);

  // Enhanced fetchMatchesByFilter that resets polling state
  const fetchMatchesByFilter = useCallback(async (filter: FilterType, category?: string) => {
    console.log('🔄 Fetching matches by filter, resetting polling state...');
    setHasPollingUpdates(false);
    setPollingMatches([]);
    setUpdatedMatchesCount(0);
    await baseFetchMatchesByFilter(filter, category);
  }, [baseFetchMatchesByFilter]);

  // Fetch live matches for comparison
  const fetchLiveMatches = useCallback(async () => {
    if (liveMatches.length === 0) return;

    try {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`🔄 [${timestamp}] Polling ${liveMatches.length} live matches...`);

      // Determine fetch parameters based on current filter
      let fetchParams;
      const today = new Date();
      const todayString = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;

      switch (currentFilter) {
        case FILTER_TYPES.LIVE:
          fetchParams = {
            category: 'football',
            status: 'live',
            limit: 20,
            offset: 0,
            sortBy: 'status,date',
            sortOrder: 'DESC,ASC'
          };
          break;
        case FILTER_TYPES.TODAY:
          fetchParams = {
            category: 'football',
            date: todayString,
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          };
          break;
        default:
          fetchParams = {
            category: 'football',
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          };
      }

      const result = await fetchMatches(fetchParams);
      const newMatches = result.data || [];

      // Find matches that have changed
      const changedMatches: MatchData[] = [];
      const liveMatchIds = liveMatches.map(m => m.id);
      const currentMatchesSnapshot = currentMatchesRef.current;

      for (const newMatch of newMatches) {
        if (liveMatchIds.includes(newMatch.id)) {
          const oldMatch = currentMatchesSnapshot.find(m => m.id === newMatch.id);
          if (oldMatch && !deepEqual(oldMatch, newMatch)) {
            changedMatches.push(newMatch);
          }
        }
      }

      if (changedMatches.length > 0) {
        console.log(`✅ [${timestamp}] Found ${changedMatches.length} updated live matches:`,
          changedMatches.map(m => ({
            id: m.id,
            status: m.status,
            score: `${m.homeTeam.score}-${m.awayTeam.score}`
          }))
        );

        // Update matches with new data - merge all new matches, not just changed ones
        const newMatchesMap = new Map(newMatches.map(m => [m.id, m]));
        const updatedMatches = currentMatchesSnapshot.map(match => {
          return newMatchesMap.get(match.id) || match;
        });

        console.log(`🔄 Updating UI with ${updatedMatches.length} matches (${changedMatches.length} changed)`);

        setPollingMatches(updatedMatches);
        setHasPollingUpdates(true);
        setLastLiveUpdate(new Date());
        setUpdatedMatchesCount(prev => prev + changedMatches.length);
      } else {
        console.log(`⏭️ [${timestamp}] No changes in live matches - skipping update`);
      }

    } catch (error) {
      console.error(`❌ Failed to fetch live matches:`, error);
    }
  }, [liveMatches.length, currentFilter]); // Removed currentMatches to avoid infinite loop

  // Start/stop polling based on live matches
  useEffect(() => {
    const shouldPoll = liveMatches.length > 0;

    if (shouldPoll) {
      console.log(`🟢 Starting live matches polling - monitoring ${liveMatches.length} live matches`);
    } else {
      console.log(`🔴 No live matches found - stopping polling`);
    }

    setIsPollingLive(shouldPoll);
  }, [liveMatches.length]);

  // Set up polling interval
  useInterval(fetchLiveMatches, isPollingLive ? 3000 : null);

  // Combine matches with order information and sort by order priority
  const matches = useMemo((): MatchDataWithOrder[] => {
    const matchesWithOrder = currentMatches.map((match) => ({
      ...match,
      hasOrder: hasOrder(match.id),
    }));

    // Sort matches: by order value (ascending), then non-order matches
    return matchesWithOrder.sort((a, b) => {
      const aOrderValue = getOrderValue(a.id);
      const bOrderValue = getOrderValue(b.id);

      // If both have order values, sort by order number (ascending)
      if (aOrderValue !== null && bOrderValue !== null) {
        return aOrderValue - bOrderValue;
      }

      // If one has order and the other doesn't, prioritize the one with order
      if (aOrderValue !== null && bOrderValue === null) return -1;
      if (aOrderValue === null && bOrderValue !== null) return 1;

      // If both don't have order, maintain original order
      return 0;
    });
  }, [currentMatches, hasOrder, getOrderValue]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    orderLoading,
    orderError,
    // Live polling related
    isPollingLive,
    liveMatchesCount: liveMatches.length,
    lastLiveUpdate,
    updatedMatchesCount,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    refreshOrderData,
    clearError,
    clearOrderError,
  };
};

export default useMatchesWithOrder;
