import { useMemo, useEffect, useRef, useCallback, useState } from 'react';
import { MatchData } from '@/types/match';
import { type FilterType, FILTER_TYPES } from '@/constants/filters';
import { useMatches } from '@/hooks/useMatches';
import { useMatchOrderMapping } from '@/hooks/useMatchOrderMapping';
import { fetchMatches } from '@/services/matchService';

// Extended MatchData with order information
export interface MatchDataWithOrder extends MatchData {
  hasOrder: boolean;
}

interface UseMatchesWithOrderReturn {
  matches: MatchDataWithOrder[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  orderLoading: boolean;
  orderError: string | null;
  // Live polling related
  isPollingLive: boolean;
  liveMatchesCount: number;
  lastLiveUpdate: Date | null;
  updatedMatchesCount: number;
  changedMatchIds: Set<string>;
  forceUpdateTrigger: number;
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
  clearOrderError: () => void;
}

// Compare essential match fields with detailed logging
const hasMatchChanged = (oldMatch: MatchData, newMatch: MatchData): boolean => {
  const changes: string[] = [];

  // Check status
  if (oldMatch.status !== newMatch.status) {
    changes.push(`status: ${oldMatch.status} → ${newMatch.status}`);
  }

  // Check home team score
  if (oldMatch.homeTeam.score !== newMatch.homeTeam.score) {
    changes.push(`homeScore: ${oldMatch.homeTeam.score} → ${newMatch.homeTeam.score}`);
  }

  // Check away team score
  if (oldMatch.awayTeam.score !== newMatch.awayTeam.score) {
    changes.push(`awayScore: ${oldMatch.awayTeam.score} → ${newMatch.awayTeam.score}`);
  }

  // Check parse data time
  if (oldMatch.parseData?.time !== newMatch.parseData?.time) {
    changes.push(`time: ${oldMatch.parseData?.time} → ${newMatch.parseData?.time}`);
  }

  // Check cards
  const oldCards = JSON.stringify(oldMatch.cards);
  const newCards = JSON.stringify(newMatch.cards);
  if (oldCards !== newCards) {
    changes.push(`cards: ${oldCards} → ${newCards}`);
  }

  // Check updatedAt
  if (oldMatch.updatedAt !== newMatch.updatedAt) {
    changes.push(`updatedAt: ${oldMatch.updatedAt} → ${newMatch.updatedAt}`);
  }

  if (changes.length > 0) {
    console.log(`🔄 Match ${newMatch.id} changed:`, changes);
    return true;
  }

  return false;
};

// Custom hook for interval
const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;
    const id = setInterval(() => savedCallback.current(), delay);
    return () => clearInterval(id);
  }, [delay]);
};

export const useMatchesWithOrder = (): UseMatchesWithOrderReturn => {
  // Use the base matches hook
  const {
    matches: baseMatches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter: baseFetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatches();

  // Live polling state
  const [pollingMatches, setPollingMatches] = useState<MatchData[]>([]);
  const [isPollingLive, setIsPollingLive] = useState(false);
  const [lastLiveUpdate, setLastLiveUpdate] = useState<Date | null>(null);
  const [updatedMatchesCount, setUpdatedMatchesCount] = useState(0);
  const [hasPollingUpdates, setHasPollingUpdates] = useState(false);
  const [changedMatchIds, setChangedMatchIds] = useState<Set<string>>(new Set());
  const [forceUpdateTrigger, setForceUpdateTrigger] = useState(0);
  const currentMatchesRef = useRef<MatchData[]>([]);

  // Use the order mapping hook
  const {
    loading: orderLoading,
    error: orderError,
    hasOrder,
    getOrderValue,
    refreshOrderData,
    clearError: clearOrderError,
  } = useMatchOrderMapping();

  // Get current matches (polling updates or base matches)
  const currentMatches = hasPollingUpdates ? pollingMatches : baseMatches;

  // Update ref with current matches
  useEffect(() => {
    currentMatchesRef.current = currentMatches;
  }, [currentMatches]);

  // Get live matches
  const liveMatches = useMemo(() => {
    return currentMatches.filter(match => match.status?.toLowerCase() === 'live');
  }, [currentMatches]);

  // Enhanced fetchMatchesByFilter that resets polling state
  const fetchMatchesByFilter = useCallback(async (filter: FilterType, category?: string) => {
    console.log('🔄 Fetching matches by filter, resetting polling state...');
    setHasPollingUpdates(false);
    setPollingMatches([]);
    setUpdatedMatchesCount(0);
    await baseFetchMatchesByFilter(filter, category);
  }, [baseFetchMatchesByFilter]);

  // Fetch live matches for comparison
  const fetchLiveMatches = useCallback(async () => {
    if (liveMatches.length === 0) return;

    try {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`🔄 [${timestamp}] Polling ${liveMatches.length} live matches...`);

      // Determine fetch parameters based on current filter
      let fetchParams;
      const today = new Date();
      const todayString = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;

      switch (currentFilter) {
        case FILTER_TYPES.LIVE:
          fetchParams = {
            category: 'football',
            status: 'live',
            limit: 20,
            offset: 0,
            sortBy: 'status,date',
            sortOrder: 'DESC,ASC'
          };
          break;
        case FILTER_TYPES.TODAY:
          fetchParams = {
            category: 'football',
            date: todayString,
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          };
          break;
        default:
          fetchParams = {
            category: 'football',
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          };
      }

      const result = await fetchMatches(fetchParams);
      const newMatches = result.data || [];

      console.log(`📊 [${timestamp}] API returned ${newMatches.length} matches`);

      // Find matches that have changed
      const changedMatches: MatchData[] = [];
      const liveMatchIds = liveMatches.map(m => m.id);
      const currentMatchesSnapshot = currentMatchesRef.current;

      console.log(`🔍 [${timestamp}] Checking ${liveMatchIds.length} live matches for changes...`);

      for (const newMatch of newMatches) {
        if (liveMatchIds.includes(newMatch.id)) {
          const oldMatch = currentMatchesSnapshot.find(m => m.id === newMatch.id);
          if (oldMatch) {
            console.log(`🔍 Checking match ${newMatch.id}: ${newMatch.homeTeam.name} ${newMatch.homeTeam.score}-${newMatch.awayTeam.score} ${newMatch.awayTeam.name}`);
            if (hasMatchChanged(oldMatch, newMatch)) {
              changedMatches.push(newMatch);
            }
          } else {
            console.log(`⚠️ Old match not found for ${newMatch.id}`);
          }
        }
      }

      // Always update matches to ensure fresh data, but only increment counter if there are changes
      const newMatchesMap = new Map(newMatches.map(m => [m.id, m]));
      const updatedMatches = currentMatchesSnapshot.map(match => {
        return newMatchesMap.get(match.id) || match;
      });

      if (changedMatches.length > 0) {
        console.log(`✅ [${timestamp}] Found ${changedMatches.length} updated live matches:`,
          changedMatches.map(m => ({
            id: m.id,
            status: m.status,
            score: `${m.homeTeam.score}-${m.awayTeam.score}`
          }))
        );

        // Track which matches changed for reference
        const newChangedIds = new Set(changedMatches.map(m => m.id));
        setChangedMatchIds(newChangedIds);

        setUpdatedMatchesCount(prev => prev + changedMatches.length);
        setLastLiveUpdate(new Date());

        // Simple trigger increment for data updates
        setForceUpdateTrigger(prev => prev + 1);

        console.log(`🔄 Updated data for matches:`, Array.from(newChangedIds));
      } else {
        console.log(`⏭️ [${timestamp}] No changes detected in live matches`);
      }

      // Always update to ensure fresh data
      console.log(`🔄 Updating UI with ${updatedMatches.length} matches`);
      setPollingMatches(updatedMatches);
      setHasPollingUpdates(true);

    } catch (error) {
      console.error(`❌ Failed to fetch live matches:`, error);
    }
  }, [liveMatches.length, currentFilter]); // Removed currentMatches to avoid infinite loop

  // Start/stop polling based on live matches
  useEffect(() => {
    const shouldPoll = liveMatches.length > 0;

    if (shouldPoll) {
      console.log(`🟢 Starting live matches polling - monitoring ${liveMatches.length} live matches`);
    } else {
      console.log(`🔴 No live matches found - stopping polling`);
    }

    setIsPollingLive(shouldPoll);
  }, [liveMatches.length]);

  // Set up polling interval
  useInterval(fetchLiveMatches, isPollingLive ? 3000 : null);

  // Combine matches with order information and sort by order priority
  const matches = useMemo((): MatchDataWithOrder[] => {
    const matchesWithOrder = currentMatches.map((match) => ({
      ...match,
      hasOrder: hasOrder(match.id),
    }));

    // Sort matches: by order value (ascending), then non-order matches
    return matchesWithOrder.sort((a, b) => {
      const aOrderValue = getOrderValue(a.id);
      const bOrderValue = getOrderValue(b.id);

      // If both have order values, sort by order number (ascending)
      if (aOrderValue !== null && bOrderValue !== null) {
        return aOrderValue - bOrderValue;
      }

      // If one has order and the other doesn't, prioritize the one with order
      if (aOrderValue !== null && bOrderValue === null) return -1;
      if (aOrderValue === null && bOrderValue !== null) return 1;

      // If both don't have order, maintain original order
      return 0;
    });
  }, [currentMatches, hasOrder, getOrderValue]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    orderLoading,
    orderError,
    // Live polling related
    isPollingLive,
    liveMatchesCount: liveMatches.length,
    lastLiveUpdate,
    updatedMatchesCount,
    changedMatchIds,
    forceUpdateTrigger,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    refreshOrderData,
    clearError,
    clearOrderError,
  };
};

export default useMatchesWithOrder;
