import { useState, useRef, useCallback, useEffect } from 'react';
import { StatisticsData, MatchData } from '@/types/match';
import { fetchMatchStatistics } from '@/services/matchService';

// Local interface for statistics response from API
interface ApiStatisticsResponse {
  id: string;
  type: number;
  stats: Array<{
    team_id: string;
    goals: number;
    shots: number;
    shots_on_target: number;
    shots_off_target: number;
    ball_possession: number;
    corner_kicks: number;
    yellow_cards: number;
    red_cards: number;
    attacks: number;
    dangerous_attack: number;
    passes?: number;
    passes_accuracy?: number;
    [key: string]: unknown;
  }>;
}

// Utility function to deep compare statistics data
const deepEqual = (obj1: StatisticsData | null, obj2: StatisticsData | null): boolean => {
  if (obj1 === obj2) return true;
  if (!obj1 || !obj2) return false;
  
  const keys1 = Object.keys(obj1) as (keyof StatisticsData)[];
  const keys2 = Object.keys(obj2) as (keyof StatisticsData)[];
  
  if (keys1.length !== keys2.length) return false;
  
  for (const key of keys1) {
    const val1 = obj1[key];
    const val2 = obj2[key];
    
    if (!val1 || !val2) {
      if (val1 !== val2) return false;
      continue;
    }
    
    if (val1.home !== val2.home || val1.away !== val2.away) {
      return false;
    }
  }
  
  return true;
};

// Custom hook for interval
const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) {
      return;
    }

    const id = setInterval(() => {
      savedCallback.current();
    }, delay);

    return () => {
      clearInterval(id);
    };
  }, [delay]);
};

interface UseMatchStatisticsOptions {
  pollingInterval?: number;
  enablePolling?: boolean;
}

export const useMatchStatistics = (
  matchData: MatchData | null,
  options: UseMatchStatisticsOptions = {}
) => {
  const { pollingInterval = 3000, enablePolling = true } = options;

  const [matchStats, setMatchStats] = useState<StatisticsData | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastStatsRef = useRef<StatisticsData | null>(null);



  // Process statistics data from API response
  const processStatistics = useCallback((statistics: ApiStatisticsResponse[] | null): StatisticsData | null => {
    if (!statistics || !Array.isArray(statistics) || statistics.length === 0) {
      return null;
    }

    try {
      // Find the first statistics group (full match)
      const fullMatchStats = statistics.find((stat: ApiStatisticsResponse) => stat.type === 0) || statistics[0];
      
      if (!fullMatchStats?.stats || fullMatchStats.stats.length < 2) {
        return null;
      }

      const homeStats = fullMatchStats.stats[0];
      const awayStats = fullMatchStats.stats[1];

      return {
        possession: {
          home: homeStats.ball_possession || 0,
          away: awayStats.ball_possession || 0,
        },
        corners: {
          home: homeStats.corner_kicks || 0,
          away: awayStats.corner_kicks || 0,
        },
        goals: {
          home: homeStats.goals || 0,
          away: awayStats.goals || 0,
        },
        cards: {
          home: (homeStats.yellow_cards || 0) + (homeStats.red_cards || 0),
          away: (awayStats.yellow_cards || 0) + (awayStats.red_cards || 0),
        },
        shots: {
          home: homeStats.shots || 0,
          away: awayStats.shots || 0,
        },
        attacks: {
          home: homeStats.attacks || 0,
          away: awayStats.attacks || 0,
        },
        passes: {
          home: homeStats.passes || 0,
          away: awayStats.passes || 0,
        },
      };
    } catch (err) {
      return null;
    }
  }, []);

  // Fetch statistics from API
  const fetchStats = useCallback(async () => {
    if (!matchData?.id) return;

    const timestamp = new Date().toLocaleTimeString();

    try {
      setError(null);
      const statisticsData = await fetchMatchStatistics(matchData.id);
      const processedStats = processStatistics(statisticsData as ApiStatisticsResponse[] | null);

      // Only update if statistics have changed
      if (!deepEqual(lastStatsRef.current, processedStats)) {
        lastStatsRef.current = processedStats;
        setMatchStats(processedStats);
      } 
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch match statistics';
      setError(errorMessage);
    }
  }, [matchData?.id, processStatistics]);

 

  // Start/stop polling based on match status and enablePolling option
  useEffect(() => {
    if (!enablePolling) {
      setIsPolling(false);
      return;
    }

    const shouldPoll = matchData?.status === 'live' ||
                      matchData?.status === 'in_progress' ||
                      matchData?.status === 'LIVE' ||
                      matchData?.status === 'IN_PROGRESS';



    setIsPolling(shouldPoll);
  }, [matchData?.status, matchData?.id, enablePolling]);

  // Poll for statistics updates
  useInterval(fetchStats, isPolling ? pollingInterval : null);

  // Manual refresh function
  const refreshStats = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    matchStats,
    isPolling,
    error,
    refreshStats,
  };
};
