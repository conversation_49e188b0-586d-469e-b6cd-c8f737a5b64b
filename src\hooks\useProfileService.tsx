"use client";

import { useSupabase } from "@/contexts/SupabaseContext";
import { ProfileService } from "@/services/profile.service";
import type { UpdateProfileData } from "@/types";
import { useCallback, useState } from "react";

export function useProfileService() {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(false);

  const getProfile = useCallback(
    async (userId: string) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { profile, error } = await profileService.getProfile(userId);
        return { profile, error };
      } catch (error) {
        return {
          profile: null,
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const updateProfile = useCallback(
    async (userId: string, updates: UpdateProfileData) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { profile, error } = await profileService.updateProfile(
          userId,
          updates
        );
        return { profile, error };
      } catch (error) {
        return {
          profile: null,
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const getAllProfiles = useCallback(async () => {
    try {
      setLoading(true);
      const profileService = new ProfileService(supabase);
      const { profiles, error } = await profileService.getAllProfiles();
      return { profiles, error };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  const getProfilesByRole = useCallback(
    async (role: string) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { profiles, error } = await profileService.getProfilesByRole(
          role
        );
        return { profiles, error };
      } catch (error) {
        return {
          profiles: [],
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const searchProfiles = useCallback(
    async (query: string) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { profiles, error } = await profileService.searchProfiles(query);
        return { profiles, error };
      } catch (error) {
        return {
          profiles: [],
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const deleteProfile = useCallback(
    async (userId: string) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { error } = await profileService.deleteProfile(userId);
        return { error };
      } catch (error) {
        return {
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const uploadAvatar = useCallback(
    async (userId: string, file: File) => {
      try {
        setLoading(true);
        const profileService = new ProfileService(supabase);
        const { url, error } = await profileService.uploadAvatar(userId, file);
        return { url, error };
      } catch (error) {
        return {
          url: null,
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  return {
    loading,
    getProfile,
    updateProfile,
    getAllProfiles,
    getProfilesByRole,
    searchProfiles,
    deleteProfile,
    uploadAvatar,
  };
}
