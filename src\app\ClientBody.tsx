"use client";

import { useEffect, useState } from "react";

export default function ClientBody({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isClient, setIsClient] = useState(false);

  // Remove any extension-added classes during hydration
  useEffect(() => {
    setIsClient(true);
    // This runs only on the client after hydration
    document.body.className = "antialiased";
  }, []);

  return (
    <div className="antialiased" suppressHydrationWarning>
      {children}
    </div>
  );
}
