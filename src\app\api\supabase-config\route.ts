import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'disable-email-confirmation') {
      // Gọi Supabase API để tắt email confirmation
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
      
      if (!supabaseUrl || !supabaseServiceKey) {
        return NextResponse.json({ 
          success: false, 
          error: 'Missing Supabase configuration' 
        }, { status: 500 });
      }

      // Gọi Supabase Management API để cập nhật cấu hình
      const response = await fetch(`${supabaseUrl}/rest/v1/projects`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Supabase API error: ${response.statusText}`);
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Email confirmation configuration updated' 
      });
    }

    return NextResponse.json({ 
      success: false, 
      error: 'Invalid action' 
    }, { status: 400 });

  } catch (error) {
    // Error configuring Supabase
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
