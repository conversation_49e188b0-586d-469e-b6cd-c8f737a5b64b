export interface ChatMessage {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  userColor: string;
  message: string;
  timestamp: number;
  verified: boolean;
  isAdmin?: boolean;
  pinned?: boolean;
  pinnedBy?: string;
  pinnedAt?: number;
  reactions?: { [key: string]: number };
  replyTo?: { id: string; userName: string; message: string; userId: string } | null;
  isPromoMessage?: boolean;
}

const mockChats: { [matchId: string]: ChatMessage[] } = {};
const mockUserCounts: { [matchId: string]: number } = {};

export const getRandomColor = (): string => {
  const colors = [
    "bg-blue-500",
    // "bg-green-500",
    // "bg-purple-500",
    // "bg-red-500",
    // "bg-yellow-500",
    // "bg-pink-500",
    // "bg-indigo-500",
    // "bg-orange-500",
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

export const formatChatTime = (timestamp: number | null): string => {
  if (!timestamp) return "now";
  const now = new Date();
  const messageTime = new Date(timestamp);

  if (messageTime.toDateString() === now.toDateString()) {
    return messageTime.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (messageTime.toDateString() === yesterday.toDateString()) {
    return "Hôm qua";
  }

  return messageTime.toLocaleDateString();
};

const createMockChatMessages = (): ChatMessage[] => {
  const mockMessages: ChatMessage[] = [];
  const userNames = [
    "Kèo nhà cái",
    "Tạch rồi",
    "Zet",
    "BóngĐáTV",
    "FanCuồng",
    "BinhLuan247",
    "Admin",
    "SuperFan",
    "FootballExpert",
    "CasinoKing",
  ];
  const avatars = "KTZBAFSEC";
  const colors = [
    "bg-pink-500",
    "bg-purple-500",
    "bg-blue-500",
    "bg-green-500",
    "bg-yellow-500",
    "bg-red-500",
    "bg-blue-600",
    "bg-orange-500",
  ];
  const messages = [
    "Đm hiro 20 đi",
    "Lol bướm toàn bão",
    "Vài trận Astra toàn treo đài thì bóng đâu mà đá. Chết tài bàn, tài góc",
    "Trận này hay quá, đội nhà đang dẫn!",
    "Trọng tài mù quá, thẻ đỏ rõ ràng mà không thổi",
    "Cầu thủ số 10 đá hay quá, xứng đáng được gọi lên đội tuyển",
    "Chào mừng các bạn đến với trận đấu hôm nay! Hãy giữ văn minh khi bình luận nhé.",
    "Đội nhà vô địch!",
    "Đá như này thì xuống hạng chắc luôn",
    "Thủ môn cứu thua xuất sắc quá",
    "Đá thế này mà cũng đòi lên tuyển à?",
    "Đội trưởng đá hay nhất trận",
    "Đá thế này thì HLV sắp bay ghế rồi",
    "Đội khách đá quá hay, xứng đáng có điểm",
  ];

  for (let i = 0; i < 20; i++) {
    const userIndex = Math.floor(Math.random() * userNames.length);
    mockMessages.push({
      id: `mock${i + 1}`,
      userId: `user${userIndex + 1}`,
      userName: userNames[userIndex],
      userAvatar: avatars[userIndex % avatars.length],
      userColor: colors[userIndex % colors.length],
      message: messages[i % messages.length] + (i > messages.length ? ` (${Math.floor(i / messages.length)})` : ""),
      timestamp: Date.now() - (300000 - i * 15000),
      verified: userIndex === 0 || userIndex === 6,
      reactions: {},
    });
  }
  return mockMessages;
};

export const getTotalMessageCount = async (matchId: string): Promise<number> => {
  if (!mockChats[matchId]) {
    mockChats[matchId] = createMockChatMessages();
  }
  return mockChats[matchId].length;
};

export const sendChatMessage = async (
  matchId: string,
  userId: string,
  userName: string,
  userAvatar: string,
  userColor: string,
  message: string,
  verified = false,
  replyTo?: { id: string; userName: string; message: string; userId: string },
): Promise<boolean> => {
  try {
    if (!mockChats[matchId]) {
      mockChats[matchId] = [];
    }

    const newMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random()}`,
      userId,
      userName,
      userAvatar,
      userColor,
      message,
      timestamp: Date.now(),
      verified,
      replyTo,
      reactions: {},
    };

    mockChats[matchId].push(newMessage);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return true;
  } catch (error) {
    // Error sending chat message
    return false;
  }
};

export const subscribeToChat = (
  matchId: string,
  callback: (messages: ChatMessage[], totalCount?: number) => void,
  limit = 50,
  beforeTimestamp: number | null = null,
) => {
  try {
    if (!mockChats[matchId]) {
      mockChats[matchId] = createMockChatMessages();
    }

    let messages = [...mockChats[matchId]];
    
    messages.sort((a, b) => a.timestamp - b.timestamp);

    if (beforeTimestamp) {
      messages = messages.filter(msg => msg.timestamp < beforeTimestamp);
    }
    
    messages = messages.slice(-limit);
    
    const totalCount = mockChats[matchId].length;
    
    callback(messages, totalCount);

    const interval = setInterval(() => {
      if (Math.random() < 0.3) {
        const userNames = ["FanCuồng", "BinhLuan247", "SuperFan"];
        // Tạo timestamp ngẫu nhiên trong khoảng 0-30 giây trước để trộn lẫn với tin nhắn thật
        const randomDelay = Math.floor(Math.random() * 30000); // 0-30 seconds
        const mixedTimestamp = Date.now() - randomDelay;
        
        const newMessage: ChatMessage = {
          id: `auto_${Date.now()}`,
          userId: `auto_${Math.floor(Math.random() * 1000)}`,
          userName: userNames[Math.floor(Math.random() * userNames.length)],
          userAvatar: "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)],
          userColor: getRandomColor(),
          message: "Tin nhắn tự động từ hệ thống!",
          timestamp: mixedTimestamp,
          verified: false,
          reactions: {},
        };
        
        mockChats[matchId].push(newMessage);
        
        const updatedMessages = [...mockChats[matchId]].slice(-limit);
        callback(updatedMessages, mockChats[matchId].length);
      }
    }, 3000);

    return () => {
      clearInterval(interval);
    };
  } catch (error) {
    // Error subscribing to chat
    return () => {};
  }
};

export const getChatUserCount = async (matchId: string): Promise<number> => {
  if (!mockUserCounts[matchId]) {
    mockUserCounts[matchId] = Math.floor(Math.random() * 50) + 10; 
  }
  return mockUserCounts[matchId];
};

export const updateUserPresence = async (matchId: string, userId: string, isOnline: boolean): Promise<boolean> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 50));
    return true;
  } catch (error) {
    // Error updating user presence
    return false;
  }
};

export const togglePinMessage = async (
  matchId: string,
  messageId: string,
  pinned: boolean,
  adminId: string,
  adminName: string,
): Promise<boolean> => {
  try {
    if (!mockChats[matchId]) return false;
    
    const message = mockChats[matchId].find(msg => msg.id === messageId);
    if (!message) return false;
    
    mockChats[matchId].forEach(msg => {
      msg.pinned = false;
      msg.pinnedBy = undefined;
      msg.pinnedAt = undefined;
    });
    
    if (pinned) {
      message.pinned = true;
      message.pinnedBy = adminName;
      message.pinnedAt = Date.now();
    }
    
    return true;
  } catch (error) {
    // Error toggling pin status
    return false;
  }
};

export const deleteChatMessage = async (matchId: string, messageId: string): Promise<boolean> => {
  try {
    if (!mockChats[matchId]) return false;
    
    const index = mockChats[matchId].findIndex(msg => msg.id === messageId);
    if (index === -1) return false;
    
    mockChats[matchId].splice(index, 1);
    return true;
  } catch (error) {
    // Error deleting chat message
    return false;
  }
};

export const reactToChatMessage = async (
  matchId: string,
  messageId: string,
  userId: string,
  reactionType: string,
): Promise<boolean> => {
  try {
    if (!mockChats[matchId]) return false;
    
    const message = mockChats[matchId].find(msg => msg.id === messageId);
    if (!message) return false;
    
    if (!message.reactions) {
      message.reactions = {};
    }
    
    if (reactionType) {
      message.reactions[reactionType] = (message.reactions[reactionType] || 0) + 1;
    }
    
    return true;
  } catch (error) {
    // Error reacting to chat message
    return false;
  }
};

export const fetchOlderMessages = async (
  matchId: string,
  beforeTimestamp: number,
  limit = 20,
): Promise<ChatMessage[]> => {
  try {
    if (!mockChats[matchId]) {
      mockChats[matchId] = createMockChatMessages();
    }
    
    const messages = mockChats[matchId].filter(msg => msg.timestamp < beforeTimestamp);
    messages.sort((a, b) => a.timestamp - b.timestamp);
    
    return messages.slice(-limit);
  } catch (error) {
    // Error fetching older messages
    return [];
  }
};
