import { useState, useEffect, useRef, useCallback } from 'react';
import { MatchData } from '@/types/match';
import { fetchMatches } from '@/services/matchService';
import { type FilterType, FILTER_TYPES } from '@/constants/filters';

// Deep comparison for match data
const deepEqual = (obj1: MatchData, obj2: MatchData): boolean => {
  // Compare essential fields that might change during live matches
  const essentialFields: (keyof MatchData)[] = [
    'status', 'homeTeam', 'awayTeam', 'cards', 'parseData', 'updatedAt'
  ];
  
  for (const field of essentialFields) {
    const val1 = obj1[field];
    const val2 = obj2[field];
    
    if (typeof val1 === 'object' && typeof val2 === 'object') {
      if (JSON.stringify(val1) !== JSON.stringify(val2)) {
        return false;
      }
    } else if (val1 !== val2) {
      return false;
    }
  }
  
  return true;
};

// Custom hook for interval
const useInterval = (callback: () => void, delay: number | null) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) {
      console.log('⏹️ Live matches polling stopped');
      return;
    }

    console.log(`⏰ Live matches polling started - every ${delay}ms`);
    const id = setInterval(() => {
      console.log(`🔔 Polling live matches...`);
      savedCallback.current();
    }, delay);
    
    return () => {
      console.log(`🧹 Cleaning up live matches polling interval`);
      clearInterval(id);
    };
  }, [delay]);
};

interface UseLiveMatchesPollingOptions {
  pollingInterval?: number;
  enablePolling?: boolean;
}

interface UseLiveMatchesPollingReturn {
  updateMatches: (newMatches: MatchData[]) => MatchData[];
  isPolling: boolean;
  lastUpdateTime: Date | null;
  updatedMatchesCount: number;
}

export const useLiveMatchesPolling = (
  currentMatches: MatchData[],
  currentFilter: FilterType,
  currentCategory: string,
  options: UseLiveMatchesPollingOptions = {}
): UseLiveMatchesPollingReturn => {
  const { pollingInterval = 3000, enablePolling = true } = options;
  
  const [isPolling, setIsPolling] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const [updatedMatchesCount, setUpdatedMatchesCount] = useState(0);
  const lastMatchesRef = useRef<MatchData[]>([]);
  const updateCallbackRef = useRef<((matches: MatchData[]) => void) | null>(null);

  // Get live matches from current matches
  const liveMatches = currentMatches.filter(match => 
    match.status?.toLowerCase() === 'live'
  );

  // Check if we should poll (only when there are live matches)
  useEffect(() => {
    const shouldPoll = enablePolling && liveMatches.length > 0;
    
    if (shouldPoll) {
      console.log(`🟢 Starting live matches polling - found ${liveMatches.length} live matches`);
    } else if (liveMatches.length === 0) {
      console.log(`🔴 No live matches found - stopping polling`);
    } else {
      console.log(`🚫 Live matches polling disabled`);
    }
    
    setIsPolling(shouldPoll);
  }, [enablePolling, liveMatches.length]);

  // Fetch and compare live matches
  const fetchLiveMatches = useCallback(async () => {
    if (liveMatches.length === 0) return;

    try {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`🔄 [${timestamp}] Fetching live matches for comparison...`);

      // Fetch matches with same filter and category
      let result;
      switch (currentFilter) {
        case FILTER_TYPES.LIVE:
          result = await fetchMatches({
            category: currentCategory,
            status: 'live',
            limit: 20,
            offset: 0,
            sortBy: 'status,date',
            sortOrder: 'DESC,ASC'
          });
          break;
        case FILTER_TYPES.TODAY:
          const today = new Date();
          const todayString = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;
          result = await fetchMatches({
            category: currentCategory,
            date: todayString,
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          break;
        default:
          result = await fetchMatches({
            category: currentCategory,
            limit: 20,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
      }

      const newMatches = result.data || [];
      
      // Find matches that have changed
      const changedMatches: MatchData[] = [];
      const liveMatchIds = liveMatches.map(m => m.id);
      
      for (const newMatch of newMatches) {
        if (liveMatchIds.includes(newMatch.id)) {
          const oldMatch = currentMatches.find(m => m.id === newMatch.id);
          if (oldMatch && !deepEqual(oldMatch, newMatch)) {
            changedMatches.push(newMatch);
          }
        }
      }

      if (changedMatches.length > 0) {
        console.log(`✅ [${timestamp}] Found ${changedMatches.length} updated live matches:`, 
          changedMatches.map(m => ({ id: m.id, status: m.status, score: `${m.homeTeam.score}-${m.awayTeam.score}` }))
        );
        
        // Update matches through callback
        if (updateCallbackRef.current) {
          updateCallbackRef.current(newMatches);
        }
        
        setLastUpdateTime(new Date());
        setUpdatedMatchesCount(prev => prev + changedMatches.length);
      } else {
        console.log(`⏭️ [${timestamp}] No changes in live matches - skipping update`);
      }

    } catch (error) {
    }
  }, [liveMatches.length, currentFilter, currentCategory, currentMatches]);

  // Set up polling interval
  useInterval(fetchLiveMatches, isPolling ? pollingInterval : null);

  // Update matches function
  const updateMatches = useCallback((newMatches: MatchData[]): MatchData[] => {
    // Create a map of new matches for quick lookup
    const newMatchesMap = new Map(newMatches.map(match => [match.id, match]));
    
    // Update existing matches with new data
    const updatedMatches = currentMatches.map(match => {
      const newMatch = newMatchesMap.get(match.id);
      return newMatch || match;
    });
    
    lastMatchesRef.current = updatedMatches;
    return updatedMatches;
  }, [currentMatches]);

  // Set update callback
  useEffect(() => {
    updateCallbackRef.current = (newMatches: MatchData[]) => {
      // This will be called by the parent component
      // The parent should handle the actual state update
    };
  }, []);

  return {
    updateMatches,
    isPolling,
    lastUpdateTime,
    updatedMatchesCount
  };
};
