"use client";

import { UserInfo } from "@/services/userService";

interface MatchBLVSectionProps {
  blvId?: string;
  blvInfo: UserInfo | null;
  blvLoading: boolean;
}

export default function MatchBLVSection({
  blvId,
  blvInfo,
  blvLoading,
}: MatchBLVSectionProps) {
  if (!blvId) {
    return null;
  }

  const displayName = blvInfo?.displayName;
  const photoURL = blvInfo?.photoURL;

  return (
    <div className="flex flex-col items-center gap-1 lg:gap-1.5 mb-2 lg:mb-3 relative z-10">
      {/* Avatar */}
      <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex-shrink-0">
        {blvLoading ? (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
            <div className="animate-spin h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 border-2 border-white border-t-transparent rounded-full"></div>
          </div>
        ) : photoURL ? (
          <img
            src={photoURL}
            alt={displayName}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              const parent = target.parentElement;
              if (parent) {
                parent.innerHTML = `
                  <div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                    <span class="text-white text-sm sm:text-base lg:text-lg font-bold">
                      ${displayName?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                `;
              }
            }}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
            <span className="text-white text-sm sm:text-base lg:text-lg font-bold">
              {displayName}
            </span>
          </div>
        )}
      </div>

      {/* BLV Name */}
      <span className="text-xs lg:text-sm text-gray-900 dark:text-white font-medium text-center max-w-[100px] truncate">
        {blvLoading ? "Đang tải..." : displayName}
      </span>
    </div>
  );
}