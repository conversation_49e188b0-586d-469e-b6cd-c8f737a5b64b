.newsContent {
  line-height: 1.8;
  color: #333;
}

.newsContent :global(h1) {
  font-size: 28px;
  font-weight: bold;
  margin: 24px 0 16px 0;
  color: #262626;
}

.newsContent :global(h2) {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0 12px 0;
  color: #262626;
}

.newsContent :global(h3) {
  font-size: 20px;
  font-weight: bold;
  margin: 16px 0 8px 0;
  color: #262626;
}

.newsContent :global(h4),
.newsContent :global(h5),
.newsContent :global(h6) {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0 8px 0;
  color: #262626;
}

.newsContent :global(p) {
  margin: 12px 0;
  line-height: 1.8;
}

.newsContent :global(ul) {
  margin: 12px 0;
  padding-left: 24px;
  list-style-type: disc;
}

.newsContent :global(ol) {
  margin: 12px 0;
  padding-left: 24px;
  list-style-type: decimal;
}

.newsContent :global(li) {
  margin: 4px 0;
  line-height: 1.6;
  display: list-item;
}

.newsContent :global(blockquote) {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #1890ff;
  background-color: #f6f8fa;
  font-style: italic;
}

.newsContent :global(a) {
  color: #1890ff;
  text-decoration: none;
}

.newsContent :global(a:hover) {
  text-decoration: underline;
}

.newsContent :global(img) {
  border-radius: 6px;
  max-width: 100% !important;
  height: auto !important;
  margin: 16px 0;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  width: 90%;
}

.newsContent :global(code) {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
}

.newsContent :global(pre) {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.newsContent :global(pre code) {
  background: none;
  padding: 0;
}

.newsContent :global(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.newsContent :global(th),
.newsContent :global(td) {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.newsContent :global(th) {
  background-color: #fafafa;
  font-weight: bold;
}

.newsContent :global(strong) {
  font-weight: bold;
}

.newsContent :global(em) {
  font-style: italic;
}

/* Dark mode styles */
:global(.dark) .newsContent {
  color: #e5e5e5 !important;
}

:global(.dark) .newsContent :global(h1),
:global(.dark) .newsContent :global(h2),
:global(.dark) .newsContent :global(h3),
:global(.dark) .newsContent :global(h4),
:global(.dark) .newsContent :global(h5),
:global(.dark) .newsContent :global(h6) {
  color: #ffffff !important;
}

:global(.dark) .newsContent :global(blockquote) {
  background-color: #374151;
  border-left-color: #60a5fa;
}

:global(.dark) .newsContent :global(code) {
  background-color: #374151;
  color: #e5e5e5;
}

:global(.dark) .newsContent :global(pre) {
  background-color: #374151;
}

:global(.dark) .newsContent :global(th) {
  background-color: #374151;
}

:global(.dark) .newsContent :global(th),
:global(.dark) .newsContent :global(td) {
  border-color: #4b5563;
}