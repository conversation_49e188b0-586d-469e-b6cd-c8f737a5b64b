"use client";

import {
  MatchCompetitionHeader,
  MatchStats,
  MatchStatus,
  MatchTeamsAndScore,
} from "../match";
import { UserInfo, getUserInfo } from "@/services/userService";
import { useEffect, useRef, useState } from "react";

import { MatchData } from "@/types/match";
import MatchTooltip from "../common/MatchTooltip";
import { cn } from "@/lib/utils";
import { useMatchOrderMapping } from "@/hooks/useMatchOrderMapping";

export type { MatchData };

interface MatchCardProps {
  match: MatchData;
  variant?: "default" | "compact" | "detailed";
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
  hasOrder?: boolean; // Optional prop to indicate if match has order
}

export default function MatchCard({
  match,
  variant = "default",
  className = "",
  onClick,
  style,
  hasOrder: hasOrderProp,
}: MatchCardProps) {
  const [blvInfo, setBlvInfo] = useState<UserInfo | null>(null);
  const [blvLoading, setBlvLoading] = useState(false);
  const [showStatsPopup, setShowStatsPopup] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isHoveringPopup, setIsHoveringPopup] = useState(false);
  const [isHoveringStatus, setIsHoveringStatus] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use the order mapping hook as fallback if hasOrder prop is not provided
  const { hasOrder: hasOrderFromHook } = useMatchOrderMapping();

  // Use prop if provided, otherwise use hook
  const matchHasOrder = hasOrderProp !== undefined ? hasOrderProp : hasOrderFromHook(match.id);

  // Detect mobile device
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768 || "ontouchstart" in window);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Handle click outside to close popup on mobile
  useEffect(() => {
    if (!isMobile || !showStatsPopup) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        target &&
        !target.closest("[data-stats-popup]") &&
        !target.closest("[data-status-trigger]")
      ) {
        setShowStatsPopup(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [isMobile, showStatsPopup]);

  useEffect(() => {
    const fetchBLVInfo = async () => {
      if (
        match.liveData &&
        match.liveData.length > 0 &&
        match.liveData[0].blv
      ) {
        setBlvLoading(true);
        try {
          const blvId = match.liveData[0].blv;
          const userInfo = await getUserInfo(blvId);
          setBlvInfo(userInfo);
        } catch (error) {
          // Failed to fetch BLV info
        } finally {
          setBlvLoading(false);
        }
      }
    };

    fetchBLVInfo();

    // Cleanup function to clear timeout when component unmounts
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, [match.liveData]);

  if (
    !match ||
    !match.homeTeam ||
    !match.awayTeam ||
    !match.status ||
    typeof match.homeTeam.score !== "number" ||
    typeof match.awayTeam.score !== "number"
  ) {
    return <></>;
  }

  const leagueName = match.league || "Unknown League";
  const homeTeamName = match.homeTeam.name || "Home Team";
  const awayTeamName = match.awayTeam.name || "Away Team";
  const homeTeamLogo = match.homeTeam.logo || "/icon/bong-da.svg";
  const awayTeamLogo = match.awayTeam.logo || "/icon/bong-da.svg";

  // Handle click on match card
  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    const homeTeamSlug = homeTeamName.toLowerCase().replace(/\s+/g, "-");
    const awayTeamSlug = awayTeamName.toLowerCase().replace(/\s+/g, "-");
    const matchSlug = `${homeTeamSlug}-vs-${awayTeamSlug}`;

    let videoUrl = "";
    if (match.liveData && match.liveData.length > 0 && match.liveData[0].hls) {
      videoUrl = match.liveData[0].hls;
    } else if (match.links && match.links.length > 0) {
      videoUrl = match.links[0];
    }

    if (videoUrl) {
      window.location.href = `/truc-tiep/${matchSlug}/streamer/${match.id
        }`;
    }
  };

  const handleStatusHover = (e: React.MouseEvent) => {
    // On mobile, don't show on hover
    if (isMobile) return;

    setIsHoveringStatus(true);

    // Prevent multiple rapid calls and unnecessary re-renders
    if (showStatsPopup) return;

    // Capture the currentTarget immediately to avoid null reference issues
    const target = e.currentTarget;
    if (!target) return;

    // Use requestAnimationFrame to ensure smooth positioning
    requestAnimationFrame(() => {
      try {
        const rect = target.getBoundingClientRect();
        const position = {
          x: rect.left + rect.width / 2,
          y: rect.bottom + 10,
        };
        setPopupPosition(position);
        setShowStatsPopup(true);
      } catch (error) {
        // Error getting bounding rect
        // Fallback: show popup without specific positioning
        setShowStatsPopup(true);
      }
    });
  };

  const handleStatusLeave = () => {
    // On mobile, don't handle mouse leave
    if (isMobile) return;

    setIsHoveringStatus(false);

    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    // Add a delay to prevent popup from disappearing too quickly
    hoverTimeoutRef.current = setTimeout(() => {
      if (!isHoveringPopup && !isHoveringStatus) {
        setShowStatsPopup(false);
      }
    }, 300);
  };

  const handleStatusClick = (e: React.MouseEvent) => {
    // Only handle click on mobile
    if (!isMobile) return;

    e.preventDefault();
    e.stopPropagation();

    if (showStatsPopup) {
      setShowStatsPopup(false);
      return;
    }
    // Capture the currentTarget immediately to avoid null reference issues
    const target = e.currentTarget;
    if (!target) return;

    // Calculate position relative to viewport
    const rect = target.getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.bottom + 10,
    };
    setPopupPosition(position);
    setShowStatsPopup(true);
  };

  const cardContent = (
    <div
      className={cn(
        "relative",
        "p-2 lg:p-4",
        "h-full",
        matchHasOrder &&
        "border-2 border-red-500 rounded-lg shadow-lg shadow-red-500/50 live-glow-animation"
      )}
      style={matchHasOrder ? {
        animation: "live-glow 2s ease-in-out infinite"
      } : undefined}
    >
      {/* Competition Header */}
      <MatchCompetitionHeader
        variant={variant}
        leagueName={leagueName}
        matchStatus={match.status}
        matchTime={match.time || ""}
        matchStats={[]}
        homeTeamName={homeTeamName}
        awayTeamName={awayTeamName}
        matchId={match.id || ""}
        isMobile={isMobile}
        showStatsPopup={showStatsPopup}
        onStatusHover={handleStatusHover}
        onStatusLeave={handleStatusLeave}
        onStatusClick={handleStatusClick}
      />

      {/* Teams and Score */}
      <MatchTeamsAndScore
        variant={variant}
        homeTeamName={homeTeamName}
        awayTeamName={awayTeamName}
        homeTeamLogo={homeTeamLogo}
        awayTeamLogo={awayTeamLogo}
        homeTeamScore={match.homeTeam.score}
        awayTeamScore={match.awayTeam.score}
      />

      {/* Divider */}
      <div className="flex justify-center mb-2 lg:mb-3 relative z-10">
        <div className="w-1/3 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
      </div>

      {/* Match Status */}
      <MatchStatus match={match} />

      {/* Statistics */}
      <MatchStats
        variant={variant}
        match={match}
        blvInfo={blvInfo}
        blvLoading={blvLoading}
      />
    </div>
  );

  // If no href provided, create default navigation URL
  const flvValue =
    typeof match.flv === "string" ? match.flv : match.flv?.url || "a";
  const defaultHref = `/truc-tiep/${match.id || "match"}/${flvValue}/${match.id || "id"
    }`;

  return (
    <>
      <div
        className={`relative rounded-lg border border-gray-200 dark:border-custom-dark-secondary bg-white dark:bg-custom-dark overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 cursor-pointer ${className}`}
        style={{
          ...style,
          backgroundImage: "url(/bg/bg-card.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        onClick={handleClick}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-custom-dark/70 dark:to-custom-dark/60 z-0"></div>
        {cardContent}
      </div>

      {/* Mobile Stats Popup */}
      {isMobile && showStatsPopup && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          data-stats-popup
        >
          <div className=" bg-black rounded-lg max-w-md w-[85vw] max-h-[80vh] overflow-y-auto">
            <div className="p-4 mx-2">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-white ">
                  Thống kê trận đấu
                </h3>
                <button
                  type="button"
                  onClick={() => setShowStatsPopup(false)}
                  className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  X
                </button>
              </div>
              <MatchTooltip
                matchId={match.key_sync || match.id || ""}
                homeTeamName={homeTeamName}
                awayTeamName={awayTeamName}
                leagueName={leagueName}
                matchStatus={match.status}
                statistics={match.statistics || []}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
