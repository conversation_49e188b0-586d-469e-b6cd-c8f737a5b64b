import { DEFAULT_FALLBACK_VIDEO_URL } from '@/constants/defaultUrl';
import { MatchData } from '@/types/match';

/**
 * Get appropriate video URL based on match status and timing
 * @param matchData - Match data from API
 * @param defaultFallbackUrl - Default URL if no other options available
 * @returns Video URL to use
 */
export const getVideoUrl = (
  matchData: MatchData | null, 
  defaultFallbackUrl: string = DEFAULT_FALLBACK_VIDEO_URL
): string => {
  if (!matchData) {
    return defaultFallbackUrl;
  }

  // Check if match is finished and over 10 minutes since creation
  if (matchData.status?.toLowerCase() === 'finished') {
    try {

      
        // Use default link from links array when match finished > 10 minutes
        const fallbackUrl = matchData.links?.[0] || defaultFallbackUrl;
        return fallbackUrl;
    } catch (error) {
      console.warn('Error parsing createdAt date:', matchData.createdAt, error);
    }
  }
  
  // Normal priority: HLS from liveData first
  if (matchData.liveData && matchData.liveData.length > 0) {
    const liveDataItem = matchData.liveData[0];
    
    // Check for HLS URL
    if (liveDataItem.hls) {
      return liveDataItem.hls;
    }
    
    // Check for FLV URL as secondary option
    if (liveDataItem.flv) {
      return liveDataItem.flv;
    }
  }
  
  // Check FLV object
  if (matchData.flv?.url) {
    return matchData.flv.url;
  }
  
  // Fallback to links array
  if (matchData.links && matchData.links.length > 0) {
    return matchData.links[0];
  }
  
  // Final fallback
  return defaultFallbackUrl;
};

/**
 * Check if match should use fallback URL based on finished status and time
 * @param matchData - Match data from API
 * @param minutesThreshold - Minutes threshold after match creation (default: 10)
 * @returns boolean indicating if should use fallback
 */
export const shouldUseFallbackUrl = (
  matchData: MatchData | null, 
  minutesThreshold: number = 10
): boolean => {
  if (!matchData || !matchData.createdAt) {
    return false;
  }

  if (matchData.status?.toLowerCase() !== 'finished') {
    return false;
  }

  try {
    const createdAt = new Date(matchData.createdAt);
    const now = new Date();
    const diffInMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60);
    
    return diffInMinutes > minutesThreshold;
  } catch (error) {
    console.warn('Error checking fallback condition:', error);
    return false;
  }
};

/**
 * Get video source type based on URL
 * @param url - Video URL
 * @returns Source type string
 */
export const getVideoSourceType = (url: string): 'hls' | 'flv' | 'mp4' | 'external' => {
  if (!url) return 'external';
  
  const lowerUrl = url.toLowerCase();
  
  if (lowerUrl.includes('.m3u8') || lowerUrl.includes('hls')) {
    return 'hls';
  }
  
  if (lowerUrl.includes('.flv') || lowerUrl.includes('flv')) {
    return 'flv';
  }
  
  if (lowerUrl.includes('.mp4')) {
    return 'mp4';
  }
  
  return 'external';
};

/**
 * Get video quality info from match data
 * @param matchData - Match data from API
 * @returns Quality information
 */
export const getVideoQuality = (matchData: MatchData | null): string => {
  if (!matchData) return 'Unknown';
  
  // Check FLV quality
  if (matchData.flv?.quality) {
    return matchData.flv.quality;
  }
  
  // Check liveData for quality info
  if (matchData.liveData && matchData.liveData.length > 0) {
    const liveDataItem = matchData.liveData[0];
    if (liveDataItem.quality) {
      return liveDataItem.quality as string;
    }
  }
  
  return 'HD';
};
