import React, { useState, useEffect } from 'react';
import VideoPlayer from '@/components/common/VideoPlayer';
import { fetchMatchDetails } from '@/services/matchService';

interface VideoData {
  videoUrl: string;
  source: 'hls' | 'external';
  matchInfo: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    league: string;
    time: string;
  };
}

interface OrderVideoProps {
  className?: string;
}

const OrderVideo: React.FC<OrderVideoProps> = ({ className = '' }) => {
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchGoogleSheetsData = async (spreadsheetId: string, range: string) => {
    const response = await fetch(`/api/google-sheets?spreadsheetId=${spreadsheetId}&range=${range}`);
    const result = await response.json();
    
    if (!result.success || !result.data?.values) {
      throw new Error('<PERSON>hông thể lấy dữ liệu từ Google Sheets');
    }
    
    return result.data.values;
  };

  const fetchMatchDetail = async (matchId: string) => {
    return await fetchMatchDetails(matchId);
  };

  const fetchVideoData = async () => {
    try {
      setLoading(true);
      setError(null);

      const spreadsheetId = '1Xw1IpIyjMtuIfbWlJ9kyiwioUoYMgRCydayKBI93j3Y';
      const range = 'Order!A1:G';
      
      const sheetValues = await fetchGoogleSheetsData(spreadsheetId, range);
      const orderOneRecord = sheetValues.find((item: {order: string}) => item.order == '1');
      
      if (!orderOneRecord) {
        setError('Không tìm thấy trận đấu với order = 1');
        return;
      }
      
      const matchId = orderOneRecord.id || orderOneRecord.ID || orderOneRecord.match_id;
      
      if (!matchId) {
        setError('Không tìm thấy ID trận đấu');
        return;
      }
      
      const matchDetail = await fetchMatchDetail(matchId);
      
      const videoUrl = matchDetail?.liveData[0].hls || 'https://quantri.ngoaihangtv.xyz/wp-content/uploads/2025/04/0408.mp4';


      const source = matchDetail?.liveData[0].hls ? 'hls' : 'external';
      
      setVideoData({
        videoUrl,
        source,
        matchInfo: {
          id: matchId,
          homeTeam: matchDetail?.homeTeam.name || orderOneRecord.home_team || 'Đội nhà',
          awayTeam: matchDetail?.awayTeam.name || orderOneRecord.away_team || 'Đội khách',
          league: matchDetail?.league || orderOneRecord.league || 'Giải đấu',
          time: matchDetail?.time || orderOneRecord.match_time || new Date().toISOString()
        }
      });
      
    } catch (err) {
      setError('Lỗi khi tải dữ liệu video');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideoData();
  }, []);

  return (
    <div className={`w-full ${className}`}>
      <VideoPlayer
        videoUrl={videoData?.videoUrl || "https://quantri.ngoaihangtv.xyz/wp-content/uploads/2025/04/0408.mp4"}
        autoPlay={true}
        muted={true}
        volume={0.7}
        theme="#0f1214"
        className="match-card-enhanced"
        isJoin={true}
        breakOutContainer={true}
        onJoinClick={() => {
          if (!videoData) return;
          window.location.href = `/truc-tiep/${videoData.matchInfo.homeTeam}/${videoData.matchInfo.awayTeam}/${videoData.matchInfo.id}`;
        }}
      />
    </div>
  );
};

export default OrderVideo;