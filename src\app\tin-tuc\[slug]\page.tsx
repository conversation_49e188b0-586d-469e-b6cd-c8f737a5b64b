'use client';

import { useNewsDetail } from "@/hooks";
import Image from "next/image";
import Link from "next/link";
import { useEffect } from "react";

export default function ArticlePage({ params }: { params: Promise<{ slug: string }> }) {
  const { post, loading, error, fetchPostBySlug } = useNewsDetail();
  
  useEffect(() => {
    const loadPost = async () => {
      const resolvedParams = await params;
      await fetchPostBySlug(resolvedParams.slug);
    };
    loadPost();
  }, [params, fetchPostBySlug]);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Loading state
  if (loading) {
    return (
      <main className="min-h-screen bg-white dark:bg-custom-dark text-zinc-900 dark:text-white">
        <header className="sticky top-0 z-40 border-b border-zinc-200 dark:border-gray-700 bg-white/90 dark:bg-custom-dark/90 backdrop-blur">
          <div className="mx-auto flex max-w-7xl items-center justify-between gap-4 px-4 py-3">
            <Link href="/tin-tuc" className="text-sm font-semibold hover:underline">Tin tức</Link>
          </div>
        </header>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Đang tải bài viết...</p>
          </div>
        </div>
      </main>
    );
  }

  // Error state
  if (error || !post) {
    return (
      <main className="min-h-screen bg-white dark:bg-custom-dark text-zinc-900 dark:text-white">
        <header className="sticky top-0 z-40 border-b border-zinc-200 dark:border-gray-700 bg-white/90 dark:bg-custom-dark/90 backdrop-blur">
          <div className="mx-auto flex max-w-7xl items-center justify-between gap-4 px-4 py-3">
            <Link href="/tin-tuc" className="text-sm font-semibold hover:underline">Tin tức</Link>
          </div>
        </header>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">
              {error || "Không tìm thấy bài viết"}
            </p>
            <Link 
              href="/tin-tuc"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Quay lại danh sách tin tức
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-white dark:bg-custom-dark text-zinc-900 dark:text-white">
      <section className="border-b border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <h1 className="text-3xl font-bold leading-snug">{post.title}</h1>
          <div className="mt-2 text-sm text-zinc-600 dark:text-gray-400">
            {post.author?.full_name && `${post.author.full_name} • `}
            {post.published_at && formatDate(post.published_at)}
          </div>
          {post.description && (
            <p className="mt-4 text-lg text-zinc-700 dark:text-gray-300">{post.description}</p>
          )}
        </div>
      </section>

      <article className="mx-auto max-w-4xl px-4 py-6">
        {post.thumbnail && (
          <div className="relative mb-6 aspect-[16/9] overflow-hidden rounded-xl border border-zinc-200 dark:border-gray-700 bg-zinc-50 dark:bg-gray-700">
            <Image src={post.thumbnail} alt={post.title} fill className="object-cover" />
          </div>
        )}
        <div className="max-w-none text-zinc-800 dark:text-zinc-200 [&>*]:mb-4 [&>*]:!bg-transparent [&>*]:!text-inherit [&>h1]:text-2xl [&>h1]:font-bold [&>h2]:text-xl [&>h2]:font-semibold [&>h3]:text-lg [&>h3]:font-medium [&>p]:leading-relaxed [&>ul]:list-disc [&>ul]:pl-6 [&>ol]:list-decimal [&>ol]:pl-6 [&>blockquote]:border-l-4 [&>blockquote]:border-zinc-300 [&>blockquote]:pl-4 [&>blockquote]:italic dark:[&>blockquote]:border-zinc-600 [&_*]:!bg-transparent [&_*]:!text-inherit">
          <div dangerouslySetInnerHTML={{ __html: post.content }} />
        </div>
      </article>
    </main>
  );
}
