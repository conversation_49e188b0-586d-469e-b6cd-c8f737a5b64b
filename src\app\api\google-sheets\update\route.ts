import { NextRequest, NextResponse } from 'next/server';
import GoogleSheetsService from '@/services/googleSheetsService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { spreadsheetId, sheetName, order, column, value } = body;

    // Validate required fields
    if (!spreadsheetId || !sheetName || !order || !column || value === undefined) {
      return NextResponse.json(
        {
          success: false,
          error: 'spreadsheetId, sheetName, order, column, and value are required'
        },
        { status: 400 }
      );
    }

    // Update the cell in Google Sheets
    await GoogleSheetsService.updateSheetCell(spreadsheetId, sheetName, order, column, value);

    return NextResponse.json({
      success: true,
      message: 'Cell updated successfully'
    });

  } catch (error) {
    // Error updating Google Sheets cell
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
