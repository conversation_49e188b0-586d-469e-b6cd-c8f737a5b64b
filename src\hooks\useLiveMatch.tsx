"use client";

import { useEffect, useState, use } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { MatchData } from "@/types/match";
import { fetchMatchDetails } from "@/services/matchService";
import { getUserInfo, UserInfo } from "@/services/userService";
import { getVideoUrl } from "@/utils/videoUrlHelper";

type Params = { match?: string; streamer?: string; id?: string };

export default function useLiveMatch(params: Promise<Params>) {
  const resolvedParams = use(params);
  const { user } = useAuth();

  // Basic states
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [videoUrl, setVideoUrl] = useState<string>(
    "https://quantri.ngoaihangtv.xyz/wp-content/uploads/2025/04/0408.mp4"
  );
  const [matchData, setMatchData] = useState<MatchData | null>(null);
  const [blvInfo, setBlvInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);

  // Extract params
  const matchId = resolvedParams.id || "unknown";
  const streamerName = resolvedParams.streamer || "a";
  const matchSlug = resolvedParams.match || "match";

  // Sync auth state
  useEffect(() => {
    setIsLoggedIn(!!user);
  }, [user]);

  // Video URL from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const videoParam = urlParams.get("video");
    const videosParam = urlParams.get("videos");

    if (videoParam) {
      setVideoUrl(videoParam);
    } else if (videosParam) {
      try {
        const videos = JSON.parse(videosParam);
        if (videos.length > 0) {
          setVideoUrl(videos[0]);
        }
      } catch (error) {
        console.error("Error parsing videos parameter:", error);
      }
    }
  }, []);

  const getMatchDetails = async () => {
    if (matchId === "unknown") return;

    try {
      setLoading(true);
      const data = await fetchMatchDetails(matchId);

      if (data) {
        setMatchData(data);

        // Set video URL using smart selection with finished match logic
        const smartVideoUrl = getVideoUrl(data);
        setVideoUrl(smartVideoUrl);

        // Get BLV info
        if (data.liveData && data.liveData.length > 0 && data.liveData[0].blv) {
          try {
            const userInfo = await getUserInfo(data.liveData[0].blv);
            setBlvInfo(userInfo);
          } catch (error) {
            console.error("Error fetching BLV info:", error);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching match details:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch match details
  useEffect(() => {
    getMatchDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchId]);

  // Auth handlers
  const openAuthModal = (mode: "login" | "register" = "login") => {
    setAuthMode(mode);
    setIsAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setIsAuthModalOpen(false);
    setAuthMode("login");
  };

  const handleLoginSuccess = () => {
    setIsLoggedIn(true);
    closeAuthModal();
  };

  return {
    // States
    isLoggedIn,
    isAuthModalOpen,
    authMode,
    videoUrl,
    matchData,
    blvInfo,
    loading,
    // Params
    matchId,
    streamerName,
    matchSlug,

    // Handlers
    openAuthModal,
    closeAuthModal,
    handleLoginSuccess,
    setAuthMode,
  };
}
