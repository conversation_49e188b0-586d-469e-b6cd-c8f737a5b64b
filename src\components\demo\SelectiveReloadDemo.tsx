"use client";

import { useState, useEffect } from "react";
import { useMatchesWithOrder } from "@/hooks/useMatchesWithOrder";
import MatchCardWithPolling from "@/components/common/MatchCardWithPolling";

export default function SelectiveReloadDemo() {
  const [renderCount, setRenderCount] = useState(0);
  
  const {
    matches,
    loading,
    isPollingLive,
    liveMatchesCount,
    lastLiveUpdate,
    updatedMatchesCount,
    changedMatchIds,
    forceUpdateTrigger,
    fetchMatchesByFilter,
  } = useMatchesWithOrder();

  // Count renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    console.log(`🔄 SelectiveReloadDemo render #${renderCount + 1}`);
    
    if (changedMatchIds.size > 0) {
      console.log(`🎯 Changed matches:`, Array.from(changedMatchIds));
    }
  });

  // Load live matches on mount
  useEffect(() => {
    console.log('🚀 Loading LIVE matches for selective reload demo...');
    fetchMatchesByFilter('live', 'football');
  }, [fetchMatchesByFilter]);

  const liveMatches = matches.filter(match => match.status?.toLowerCase() === 'live');

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            🎯 Selective Card Reload Demo
          </h1>
          
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
              <div className="text-xs text-blue-600 dark:text-blue-400">Total Renders</div>
              <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                {renderCount}
              </div>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded">
              <div className="text-xs text-green-600 dark:text-green-400">Polling</div>
              <div className={`text-lg font-bold ${isPollingLive ? 'text-green-700' : 'text-gray-500'}`}>
                {isPollingLive ? (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    ON
                  </div>
                ) : 'OFF'}
              </div>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded">
              <div className="text-xs text-purple-600 dark:text-purple-400">Live Matches</div>
              <div className="text-lg font-bold text-purple-700 dark:text-purple-300">
                {liveMatchesCount}
              </div>
            </div>
            
            <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded">
              <div className="text-xs text-orange-600 dark:text-orange-400">Updates</div>
              <div className="text-lg font-bold text-orange-700 dark:text-orange-300">
                {updatedMatchesCount}
              </div>
            </div>
            
            <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded">
              <div className="text-xs text-red-600 dark:text-red-400">Changed IDs</div>
              <div className="text-lg font-bold text-red-700 dark:text-red-300">
                {changedMatchIds.size}
              </div>
            </div>
            
            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-3 rounded">
              <div className="text-xs text-indigo-600 dark:text-indigo-400">Force Trigger</div>
              <div className="text-lg font-bold text-indigo-700 dark:text-indigo-300">
                {forceUpdateTrigger}
              </div>
            </div>
          </div>

          {/* Last Update */}
          {lastLiveUpdate && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-900 rounded border">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Last Update: <span className="font-mono">{lastLiveUpdate.toLocaleTimeString()}</span>
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            🎬 Animation Features:
          </h3>
          <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-2">
            <div><strong>🔵 Blue Ring:</strong> Appears on cards that have changed</div>
            <div><strong>✨ Flash Animation:</strong> Blue overlay flashes when card updates</div>
            <div><strong>📍 Update Indicator:</strong> Blue dot appears on top-right of changed cards</div>
            <div><strong>🎯 Scale Effect:</strong> Card slightly scales up during update</div>
            <div><strong>⚡ Selective:</strong> Only changed cards animate, others stay static</div>
          </div>
        </div>

        {/* Changed Matches Info */}
        {changedMatchIds.size > 0 && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              🎯 Currently Changed Matches:
            </h3>
            <div className="flex flex-wrap gap-2">
              {Array.from(changedMatchIds).map(id => (
                <span 
                  key={id}
                  className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded text-xs font-mono"
                >
                  {id.slice(-6)}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Live Matches with Enhanced Cards */}
        {!loading && liveMatches.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              Live Matches with Selective Reload ({liveMatches.length})
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {liveMatches.map((match, index) => {
                const isChanged = changedMatchIds.has(match.id);
                
                return (
                  <MatchCardWithPolling
                    key={match.id}
                    match={match}
                    variant="detailed"
                    className="animate-match-fade-in"
                    hasOrder={match.hasOrder}
                    isChanged={isChanged}
                    forceUpdateTrigger={forceUpdateTrigger}
                    style={{
                      animationDelay: `${index * 100}ms`,
                      animationFillMode: "both",
                    }}
                  />
                );
              })}
            </div>
          </div>
        )}

        {/* All Matches Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            All Matches Summary ({matches.length})
          </h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading matches...</p>
            </div>
          ) : matches.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-4">
              No matches found. Try switching to "Trực tiếp" tab to see live matches.
            </p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {matches.slice(0, 8).map((match) => {
                const isChanged = changedMatchIds.has(match.id);
                const isLive = match.status?.toLowerCase() === 'live';
                
                return (
                  <div
                    key={match.id}
                    className={`border rounded-lg p-3 transition-all duration-300 ${
                      isLive
                        ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900/20'
                        : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/20'
                    } ${
                      isChanged ? 'ring-2 ring-blue-400 ring-opacity-75' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className={`text-xs font-medium uppercase px-2 py-1 rounded ${
                          isLive
                            ? 'bg-red-100 text-red-600 dark:bg-red-800 dark:text-red-200'
                            : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                        }`}>
                          {match.status}
                        </span>
                        {isChanged && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                        {match.id.slice(-4)}
                      </span>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-900 dark:text-white truncate">
                          {match.homeTeam.name}
                        </span>
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {match.homeTeam.score}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-900 dark:text-white truncate">
                          {match.awayTeam.name}
                        </span>
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {match.awayTeam.score}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

      </div>
    </div>
  );
}
